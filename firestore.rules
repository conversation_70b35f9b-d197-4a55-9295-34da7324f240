rules_version = '2';

// Firestore Security Rules for Face Verification Video Metadata
service cloud.firestore {
  match /databases/{database}/documents {
    // User documents - users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      
      // Video verification metadata subcollection
      match /verificationVideos/{videoId} {
        allow read, write: if request.auth != null 
                          && request.auth.uid == userId
                          && isValidVideoMetadata();
        
        allow delete: if request.auth != null && request.auth.uid == userId;
      }
    }
    
    // Helper function to validate video metadata structure
    function isValidVideoMetadata() {
      return request.resource.data.keys().hasAll([
        'fileName', 'uploadTimestamp', 'qualityScore', 'storageRef'
      ]) && request.resource.data.qualityScore is number
         && request.resource.data.qualityScore >= 0
         && request.resource.data.qualityScore <= 100;
    }
    
    // Deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
