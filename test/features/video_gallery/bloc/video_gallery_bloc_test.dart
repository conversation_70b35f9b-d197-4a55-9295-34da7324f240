import 'package:bloc_test/bloc_test.dart';
import 'package:bloomg_flutter/features/video_gallery/bloc/video_gallery_bloc.dart';
import 'package:bloomg_flutter/features/video_gallery/bloc/video_gallery_event.dart';
import 'package:bloomg_flutter/features/video_gallery/bloc/video_gallery_state.dart';
import 'package:bloomg_flutter/features/video_storage/models/video_download_state.dart';
import 'package:bloomg_flutter/features/video_storage/models/video_metadata.dart';
import 'package:bloomg_flutter/features/video_storage/repository/firebase_video_storage_repository.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockFirebaseVideoStorageRepository extends Mock
    implements FirebaseVideoStorageRepository {}

class MockLoggerService extends Mock implements LoggerService {}

void main() {
  group('VideoGalleryBloc', () {
    late VideoGalleryBloc videoGalleryBloc;
    late MockFirebaseVideoStorageRepository mockRepository;
    late MockLoggerService mockLogger;

    setUp(() {
      mockRepository = MockFirebaseVideoStorageRepository();
      mockLogger = MockLoggerService();
      videoGalleryBloc = VideoGalleryBloc(
        repository: mockRepository,
        logger: mockLogger,
      );
    });

    tearDown(() {
      videoGalleryBloc.close();
    });

    test('initial state is correct', () {
      expect(
        videoGalleryBloc.state,
        equals(const VideoGalleryState()),
      );
    });

    group('VideoGalleryLoaded', () {
      final testVideos = [
        VideoMetadata(
          id: 'video1',
          fileName: 'video1.mp4',
          uploadTimestamp: DateTime(2024, 1, 15),
          qualityScore: 85.0,
          storageRef: 'videos/user/video1',
        ),
        VideoMetadata(
          id: 'video2',
          fileName: 'video2.mp4',
          uploadTimestamp: DateTime(2024, 1, 16),
          qualityScore: 92.0,
          storageRef: 'videos/user/video2',
        ),
      ];

      blocTest<VideoGalleryBloc, VideoGalleryState>(
        'emits loading then loaded state when videos are fetched successfully',
        build: () {
          when(() => mockRepository.getVideosStream())
              .thenAnswer((_) => Stream.value(testVideos));
          return videoGalleryBloc;
        },
        act: (bloc) => bloc.add(const VideoGalleryLoaded()),
        expect: () => [
          const VideoGalleryState(status: VideoGalleryStatus.loading),
          VideoGalleryState(
            status: VideoGalleryStatus.loaded,
            videos: testVideos,
            filteredVideos: testVideos,
          ),
        ],
        verify: (_) {
          verify(() => mockRepository.getVideosStream()).called(1);
          verify(() => mockLogger.info('[VIDEO_GALLERY] Action: Loading videos')).called(1);
        },
      );

      blocTest<VideoGalleryBloc, VideoGalleryState>(
        'emits error state when video fetching fails',
        build: () {
          when(() => mockRepository.getVideosStream())
              .thenAnswer((_) => Stream.error('Network error'));
          return videoGalleryBloc;
        },
        act: (bloc) => bloc.add(const VideoGalleryLoaded()),
        expect: () => [
          const VideoGalleryState(status: VideoGalleryStatus.loading),
          const VideoGalleryState(
            status: VideoGalleryStatus.error,
            error: 'Network error',
          ),
        ],
        verify: (_) {
          verify(() => mockRepository.getVideosStream()).called(1);
          verify(() => mockLogger.error('[VIDEO_GALLERY] Action: Error loading videos - Network error')).called(1);
        },
      );
    });

    group('VideoGalleryFilterChanged', () {
      final testVideos = [
        VideoMetadata(
          id: 'video1',
          fileName: 'video1.mp4',
          uploadTimestamp: DateTime(2024, 1, 10), // 5+ days ago
          qualityScore: 85.0, // High quality
          storageRef: 'videos/user/video1',
        ),
        VideoMetadata(
          id: 'video2',
          fileName: 'video2.mp4',
          uploadTimestamp: DateTime.now().subtract(const Duration(days: 2)), // Recent
          qualityScore: 75.0, // Good quality
          storageRef: 'videos/user/video2',
        ),
        VideoMetadata(
          id: 'video3',
          fileName: 'video3.mp4',
          uploadTimestamp: DateTime(2024, 1, 5),
          qualityScore: 45.0, // Poor quality
          storageRef: 'videos/user/video3',
        ),
      ];

      blocTest<VideoGalleryBloc, VideoGalleryState>(
        'filters videos by high quality (>= 80%)',
        build: () => videoGalleryBloc,
        seed: () => VideoGalleryState(
          status: VideoGalleryStatus.loaded,
          videos: testVideos,
          filteredVideos: testVideos,
        ),
        act: (bloc) => bloc.add(
          const VideoGalleryFilterChanged(filter: VideoGalleryFilter.highQuality),
        ),
        expect: () => [
          VideoGalleryState(
            status: VideoGalleryStatus.loaded,
            videos: testVideos,
            filteredVideos: [testVideos[0]], // Only video1 with 85% quality
            filter: VideoGalleryFilter.highQuality,
          ),
        ],
      );

      blocTest<VideoGalleryBloc, VideoGalleryState>(
        'filters videos by good quality (>= 70%)',
        build: () => videoGalleryBloc,
        seed: () => VideoGalleryState(
          status: VideoGalleryStatus.loaded,
          videos: testVideos,
          filteredVideos: testVideos,
        ),
        act: (bloc) => bloc.add(
          const VideoGalleryFilterChanged(filter: VideoGalleryFilter.goodQuality),
        ),
        expect: () => [
          VideoGalleryState(
            status: VideoGalleryStatus.loaded,
            videos: testVideos,
            filteredVideos: [testVideos[0], testVideos[1]], // video1 and video2
            filter: VideoGalleryFilter.goodQuality,
          ),
        ],
      );

      blocTest<VideoGalleryBloc, VideoGalleryState>(
        'filters videos by recent (within 7 days)',
        build: () => videoGalleryBloc,
        seed: () => VideoGalleryState(
          status: VideoGalleryStatus.loaded,
          videos: testVideos,
          filteredVideos: testVideos,
        ),
        act: (bloc) => bloc.add(
          const VideoGalleryFilterChanged(filter: VideoGalleryFilter.recent),
        ),
        expect: () => [
          VideoGalleryState(
            status: VideoGalleryStatus.loaded,
            videos: testVideos,
            filteredVideos: [testVideos[1]], // Only video2 is recent
            filter: VideoGalleryFilter.recent,
          ),
        ],
      );
    });

    group('VideoGallerySortChanged', () {
      final testVideos = [
        VideoMetadata(
          id: 'video1',
          fileName: 'a_video.mp4',
          uploadTimestamp: DateTime(2024, 1, 15),
          qualityScore: 85.0,
          storageRef: 'videos/user/video1',
          fileSize: 1000000,
        ),
        VideoMetadata(
          id: 'video2',
          fileName: 'z_video.mp4',
          uploadTimestamp: DateTime(2024, 1, 10),
          qualityScore: 92.0,
          storageRef: 'videos/user/video2',
          fileSize: 2000000,
        ),
      ];

      blocTest<VideoGalleryBloc, VideoGalleryState>(
        'sorts videos by upload date descending',
        build: () => videoGalleryBloc,
        seed: () => VideoGalleryState(
          status: VideoGalleryStatus.loaded,
          videos: testVideos,
          filteredVideos: testVideos,
        ),
        act: (bloc) => bloc.add(
          const VideoGallerySortChanged(
            sortBy: VideoSortField.uploadDate,
            ascending: false,
          ),
        ),
        expect: () => [
          VideoGalleryState(
            status: VideoGalleryStatus.loaded,
            videos: testVideos,
            filteredVideos: [testVideos[0], testVideos[1]], // video1 first (newer)
            sortBy: VideoSortField.uploadDate,
            sortAscending: false,
          ),
        ],
      );

      blocTest<VideoGalleryBloc, VideoGalleryState>(
        'sorts videos by quality score ascending',
        build: () => videoGalleryBloc,
        seed: () => VideoGalleryState(
          status: VideoGalleryStatus.loaded,
          videos: testVideos,
          filteredVideos: testVideos,
        ),
        act: (bloc) => bloc.add(
          const VideoGallerySortChanged(
            sortBy: VideoSortField.qualityScore,
            ascending: true,
          ),
        ),
        expect: () => [
          VideoGalleryState(
            status: VideoGalleryStatus.loaded,
            videos: testVideos,
            filteredVideos: [testVideos[0], testVideos[1]], // video1 first (85 < 92)
            sortBy: VideoSortField.qualityScore,
            sortAscending: true,
          ),
        ],
      );
    });

    group('VideoDownloadStarted', () {
      final testVideo = VideoMetadata(
        id: 'video1',
        fileName: 'video1.mp4',
        uploadTimestamp: DateTime(2024, 1, 15),
        qualityScore: 85.0,
        storageRef: 'videos/user/video1',
      );

      blocTest<VideoGalleryBloc, VideoGalleryState>(
        'starts video download and updates download state',
        build: () {
          when(() => mockRepository.downloadVideo(any()))
              .thenAnswer((_) async => '/local/path/video1.mp4');
          return videoGalleryBloc;
        },
        act: (bloc) => bloc.add(VideoDownloadStarted(video: testVideo)),
        expect: () => [
          VideoGalleryState(
            downloadStates: {
              'video1': const VideoDownloadState(
                videoId: 'video1',
                status: VideoDownloadStatus.downloading,
              ),
            },
          ),
          VideoGalleryState(
            downloadStates: {
              'video1': const VideoDownloadState(
                videoId: 'video1',
                status: VideoDownloadStatus.downloaded,
                localPath: '/local/path/video1.mp4',
              ),
            },
          ),
        ],
        verify: (_) {
          verify(() => mockRepository.downloadVideo(testVideo)).called(1);
          verify(() => mockLogger.info('[VIDEO_GALLERY] Action: Starting download for video1')).called(1);
          verify(() => mockLogger.info('[VIDEO_GALLERY] Action: Download completed for video1')).called(1);
        },
      );

      blocTest<VideoGalleryBloc, VideoGalleryState>(
        'handles download failure',
        build: () {
          when(() => mockRepository.downloadVideo(any()))
              .thenThrow('Download failed');
          return videoGalleryBloc;
        },
        act: (bloc) => bloc.add(VideoDownloadStarted(video: testVideo)),
        expect: () => [
          VideoGalleryState(
            downloadStates: {
              'video1': const VideoDownloadState(
                videoId: 'video1',
                status: VideoDownloadStatus.downloading,
              ),
            },
          ),
          VideoGalleryState(
            downloadStates: {
              'video1': const VideoDownloadState(
                videoId: 'video1',
                status: VideoDownloadStatus.failed,
                error: 'Download failed',
              ),
            },
          ),
        ],
        verify: (_) {
          verify(() => mockRepository.downloadVideo(testVideo)).called(1);
          verify(() => mockLogger.error('[VIDEO_GALLERY] Action: Download failed for video1 - Download failed')).called(1);
        },
      );
    });

    group('VideoDeleted', () {
      final testVideo = VideoMetadata(
        id: 'video1',
        fileName: 'video1.mp4',
        uploadTimestamp: DateTime(2024, 1, 15),
        qualityScore: 85.0,
        storageRef: 'videos/user/video1',
      );

      blocTest<VideoGalleryBloc, VideoGalleryState>(
        'deletes video successfully',
        build: () {
          when(() => mockRepository.deleteVideo(any()))
              .thenAnswer((_) async {});
          return videoGalleryBloc;
        },
        act: (bloc) => bloc.add(VideoDeleted(video: testVideo)),
        expect: () => [],
        verify: (_) {
          verify(() => mockRepository.deleteVideo(testVideo)).called(1);
          verify(() => mockLogger.info('[VIDEO_GALLERY] Action: Deleting video1')).called(1);
        },
      );

      blocTest<VideoGalleryBloc, VideoGalleryState>(
        'handles delete failure',
        build: () {
          when(() => mockRepository.deleteVideo(any()))
              .thenThrow('Delete failed');
          return videoGalleryBloc;
        },
        act: (bloc) => bloc.add(VideoDeleted(video: testVideo)),
        expect: () => [
          const VideoGalleryState(
            status: VideoGalleryStatus.error,
            error: 'Failed to delete video: Delete failed',
          ),
        ],
        verify: (_) {
          verify(() => mockRepository.deleteVideo(testVideo)).called(1);
          verify(() => mockLogger.error('[VIDEO_GALLERY] Action: Delete failed for video1 - Delete failed')).called(1);
        },
      );
    });
  });
}
