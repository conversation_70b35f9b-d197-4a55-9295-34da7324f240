import 'package:bloc_test/bloc_test.dart';
import 'package:bloomg_flutter/features/video_gallery/bloc/video_gallery_bloc.dart';
import 'package:bloomg_flutter/features/video_gallery/bloc/video_gallery_event.dart';
import 'package:bloomg_flutter/features/video_gallery/bloc/video_gallery_state.dart';
import 'package:bloomg_flutter/features/video_gallery/view/video_gallery_screen.dart';
import 'package:bloomg_flutter/features/video_storage/models/video_metadata.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:go_router/go_router.dart';
import 'package:mocktail/mocktail.dart';

import '../../../helpers/pump_app.dart';

class MockVideoGalleryBloc
    extends MockBloc<VideoGalleryEvent, VideoGalleryState>
    implements VideoGalleryBloc {}

class MockGoRouter extends Mock implements GoRouter {}

void main() {
  group('VideoGalleryScreen', () {
    late MockVideoGalleryBloc mockVideoGalleryBloc;

    setUp(() {
      mockVideoGalleryBloc = MockVideoGalleryBloc();
    });

    testWidgets('renders VideoGalleryView', (tester) async {
      when(() => mockVideoGalleryBloc.state)
          .thenReturn(const VideoGalleryState());

      await tester.pumpApp(
        BlocProvider.value(
          value: mockVideoGalleryBloc,
          child: const VideoGalleryScreen(),
        ),
      );

      expect(find.byType(VideoGalleryView), findsOneWidget);
    });
  });

  group('VideoGalleryView', () {
    late MockVideoGalleryBloc mockVideoGalleryBloc;

    setUp(() {
      mockVideoGalleryBloc = MockVideoGalleryBloc();
    });

    testWidgets('displays loading indicator when loading', (tester) async {
      when(() => mockVideoGalleryBloc.state).thenReturn(
        const VideoGalleryState(status: VideoGalleryStatus.loading),
      );

      await tester.pumpApp(
        BlocProvider.value(
          value: mockVideoGalleryBloc,
          child: const VideoGalleryView(),
        ),
      );

      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Loading videos...'), findsOneWidget);
    });

    testWidgets('displays error message when error occurs', (tester) async {
      when(() => mockVideoGalleryBloc.state).thenReturn(
        const VideoGalleryState(
          status: VideoGalleryStatus.error,
          error: 'Network error',
        ),
      );

      await tester.pumpApp(
        BlocProvider.value(
          value: mockVideoGalleryBloc,
          child: const VideoGalleryView(),
        ),
      );

      expect(find.byIcon(Icons.error_outline), findsOneWidget);
      expect(find.text('Error loading videos'), findsOneWidget);
      expect(find.text('Network error'), findsOneWidget);
      expect(find.text('Retry'), findsOneWidget);
    });

    testWidgets('displays empty state when no videos', (tester) async {
      when(() => mockVideoGalleryBloc.state).thenReturn(
        const VideoGalleryState(
          status: VideoGalleryStatus.loaded,
        ),
      );

      await tester.pumpApp(
        BlocProvider.value(
          value: mockVideoGalleryBloc,
          child: const VideoGalleryView(),
        ),
      );

      expect(find.byIcon(Icons.video_library_outlined), findsOneWidget);
      expect(find.text('No videos found'), findsOneWidget);
      expect(
        find.text('Record your first face verification video'),
        findsOneWidget,
      );
      expect(find.text('Record Video'), findsWidgets); // Button text
    });

    testWidgets('displays video grid when videos are loaded', (tester) async {
      final testVideos = [
        VideoMetadata(
          id: 'video1',
          fileName: 'video1.mp4',
          uploadTimestamp: DateTime(2024, 1, 15),
          qualityScore: 85,
          storageRef: 'videos/user/video1',
        ),
        VideoMetadata(
          id: 'video2',
          fileName: 'video2.mp4',
          uploadTimestamp: DateTime(2024, 1, 16),
          qualityScore: 92,
          storageRef: 'videos/user/video2',
        ),
      ];

      when(() => mockVideoGalleryBloc.state).thenReturn(
        VideoGalleryState(
          status: VideoGalleryStatus.loaded,
          videos: testVideos,
          filteredVideos: testVideos,
        ),
      );

      await tester.pumpApp(
        BlocProvider.value(
          value: mockVideoGalleryBloc,
          child: const VideoGalleryView(),
        ),
      );

      expect(find.byType(GridView), findsOneWidget);
      expect(find.text('2 videos'), findsOneWidget);
    });

    testWidgets('displays filter bar', (tester) async {
      when(() => mockVideoGalleryBloc.state).thenReturn(
        const VideoGalleryState(status: VideoGalleryStatus.loaded),
      );

      await tester.pumpApp(
        BlocProvider.value(
          value: mockVideoGalleryBloc,
          child: const VideoGalleryView(),
        ),
      );

      expect(find.text('All'), findsOneWidget);
      expect(find.text('High Quality'), findsOneWidget);
      expect(find.text('Good Quality'), findsOneWidget);
      expect(find.text('Recent'), findsOneWidget);
    });

    testWidgets('displays floating action button', (tester) async {
      when(() => mockVideoGalleryBloc.state).thenReturn(
        const VideoGalleryState(status: VideoGalleryStatus.loaded),
      );

      await tester.pumpApp(
        BlocProvider.value(
          value: mockVideoGalleryBloc,
          child: const VideoGalleryView(),
        ),
      );

      expect(find.byType(FloatingActionButton), findsOneWidget);
      expect(find.byIcon(Icons.face), findsOneWidget);
      expect(find.text('Record Video'), findsOneWidget);
    });

    testWidgets('shows snackbar on error', (tester) async {
      whenListen(
        mockVideoGalleryBloc,
        Stream.fromIterable([
          const VideoGalleryState(),
          const VideoGalleryState(
            status: VideoGalleryStatus.error,
            error: 'Test error',
          ),
        ]),
      );

      when(() => mockVideoGalleryBloc.state).thenReturn(
        const VideoGalleryState(),
      );

      await tester.pumpApp(
        BlocProvider.value(
          value: mockVideoGalleryBloc,
          child: const VideoGalleryView(),
        ),
      );

      await tester.pump();

      expect(find.text('Error: Test error'), findsOneWidget);
      expect(find.text('Retry'), findsOneWidget);
    });

    testWidgets('refresh indicator triggers refresh', (tester) async {
      when(() => mockVideoGalleryBloc.state).thenReturn(
        const VideoGalleryState(
          status: VideoGalleryStatus.loaded,
        ),
      );

      await tester.pumpApp(
        BlocProvider.value(
          value: mockVideoGalleryBloc,
          child: const VideoGalleryView(),
        ),
      );

      await tester.fling(
        find.byType(RefreshIndicator),
        const Offset(0, 300),
        1000,
      );
      await tester.pump();

      verify(() => mockVideoGalleryBloc.add(const VideoGalleryRefreshed()))
          .called(1);
    });

    group('app bar actions', () {
      testWidgets('displays sort and home buttons', (tester) async {
        when(() => mockVideoGalleryBloc.state).thenReturn(
          const VideoGalleryState(status: VideoGalleryStatus.loaded),
        );

        await tester.pumpApp(
          BlocProvider.value(
            value: mockVideoGalleryBloc,
            child: const VideoGalleryView(),
          ),
        );

        expect(find.byIcon(Icons.sort), findsOneWidget);
        expect(find.byIcon(Icons.home), findsOneWidget);
      });

      testWidgets('sort button shows sort options', (tester) async {
        when(() => mockVideoGalleryBloc.state).thenReturn(
          const VideoGalleryState(status: VideoGalleryStatus.loaded),
        );

        await tester.pumpApp(
          BlocProvider.value(
            value: mockVideoGalleryBloc,
            child: const VideoGalleryView(),
          ),
        );

        await tester.tap(find.byIcon(Icons.sort));
        await tester.pumpAndSettle();

        expect(find.text('Upload Date'), findsOneWidget);
        expect(find.text('Quality Score'), findsOneWidget);
        expect(find.text('File Name'), findsOneWidget);
        expect(find.text('File Size'), findsOneWidget);
      });
    });

    group('video count display', () {
      testWidgets('shows correct count for single video', (tester) async {
        final testVideos = [
          VideoMetadata(
            id: 'video1',
            fileName: 'video1.mp4',
            uploadTimestamp: DateTime(2024, 1, 15),
            qualityScore: 85,
            storageRef: 'videos/user/video1',
          ),
        ];

        when(() => mockVideoGalleryBloc.state).thenReturn(
          VideoGalleryState(
            status: VideoGalleryStatus.loaded,
            videos: testVideos,
            filteredVideos: testVideos,
          ),
        );

        await tester.pumpApp(
          BlocProvider.value(
            value: mockVideoGalleryBloc,
            child: const VideoGalleryView(),
          ),
        );

        expect(find.text('1 video'), findsOneWidget);
      });

      testWidgets('shows correct count for multiple videos', (tester) async {
        final testVideos = List.generate(
          5,
          (index) => VideoMetadata(
            id: 'video$index',
            fileName: 'video$index.mp4',
            uploadTimestamp: DateTime(2024, 1, 15 + index),
            qualityScore: 80.0 + index,
            storageRef: 'videos/user/video$index',
          ),
        );

        when(() => mockVideoGalleryBloc.state).thenReturn(
          VideoGalleryState(
            status: VideoGalleryStatus.loaded,
            videos: testVideos,
            filteredVideos: testVideos,
          ),
        );

        await tester.pumpApp(
          BlocProvider.value(
            value: mockVideoGalleryBloc,
            child: const VideoGalleryView(),
          ),
        );

        expect(find.text('5 videos'), findsOneWidget);
      });
    });
  });
}
