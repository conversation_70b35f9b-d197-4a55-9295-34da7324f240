import 'package:bloomg_flutter/features/video_storage/models/video_upload_progress.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('VideoUploadProgress', () {
    test('should create VideoUploadProgress with all properties', () {
      const progress = VideoUploadProgress(
        videoId: 'test-video-id',
        status: VideoUploadStatus.uploading,
        progressPercentage: 45.5,
        bytesTransferred: 1024000,
        totalBytes: 2048000,
        error: null,
      );

      expect(progress.videoId, equals('test-video-id'));
      expect(progress.status, equals(VideoUploadStatus.uploading));
      expect(progress.progressPercentage, equals(45.5));
      expect(progress.bytesTransferred, equals(1024000));
      expect(progress.totalBytes, equals(2048000));
      expect(progress.error, isNull);
    });

    test('should create VideoUploadProgress with minimal properties', () {
      const progress = VideoUploadProgress(
        videoId: 'minimal-id',
        status: VideoUploadStatus.pending,
      );

      expect(progress.videoId, equals('minimal-id'));
      expect(progress.status, equals(VideoUploadStatus.pending));
      expect(progress.progressPercentage, equals(0.0));
      expect(progress.bytesTransferred, equals(0));
      expect(progress.totalBytes, equals(0));
      expect(progress.error, isNull);
    });

    group('status checks', () {
      test('isPending should return true for pending status', () {
        const progress = VideoUploadProgress(
          videoId: 'test',
          status: VideoUploadStatus.pending,
        );
        expect(progress.isPending, isTrue);
        expect(progress.isUploading, isFalse);
        expect(progress.isCompleted, isFalse);
        expect(progress.isFailed, isFalse);
      });

      test('isUploading should return true for uploading status', () {
        const progress = VideoUploadProgress(
          videoId: 'test',
          status: VideoUploadStatus.uploading,
        );
        expect(progress.isPending, isFalse);
        expect(progress.isUploading, isTrue);
        expect(progress.isCompleted, isFalse);
        expect(progress.isFailed, isFalse);
      });

      test('isCompleted should return true for completed status', () {
        const progress = VideoUploadProgress(
          videoId: 'test',
          status: VideoUploadStatus.completed,
        );
        expect(progress.isPending, isFalse);
        expect(progress.isUploading, isFalse);
        expect(progress.isCompleted, isTrue);
        expect(progress.isFailed, isFalse);
      });

      test('isFailed should return true for failed status', () {
        const progress = VideoUploadProgress(
          videoId: 'test',
          status: VideoUploadStatus.failed,
          error: 'Upload failed',
        );
        expect(progress.isPending, isFalse);
        expect(progress.isUploading, isFalse);
        expect(progress.isCompleted, isFalse);
        expect(progress.isFailed, isTrue);
      });
    });

    group('copyWith', () {
      test('should create copy with updated status', () {
        const original = VideoUploadProgress(
          videoId: 'test-id',
          status: VideoUploadStatus.pending,
        );

        final updated = original.copyWith(
          status: VideoUploadStatus.uploading,
          progressPercentage: 25.0,
        );

        expect(updated.videoId, equals('test-id'));
        expect(updated.status, equals(VideoUploadStatus.uploading));
        expect(updated.progressPercentage, equals(25.0));
        expect(updated.bytesTransferred, equals(0)); // Unchanged
        expect(updated.totalBytes, equals(0)); // Unchanged
        expect(updated.error, isNull); // Unchanged
      });

      test('should create copy with progress update', () {
        const original = VideoUploadProgress(
          videoId: 'test-id',
          status: VideoUploadStatus.uploading,
          progressPercentage: 30.0,
          bytesTransferred: 300000,
          totalBytes: 1000000,
        );

        final updated = original.copyWith(
          progressPercentage: 60.0,
          bytesTransferred: 600000,
        );

        expect(updated.videoId, equals('test-id'));
        expect(updated.status, equals(VideoUploadStatus.uploading));
        expect(updated.progressPercentage, equals(60.0));
        expect(updated.bytesTransferred, equals(600000));
        expect(updated.totalBytes, equals(1000000)); // Unchanged
        expect(updated.error, isNull);
      });

      test('should create copy with error', () {
        const original = VideoUploadProgress(
          videoId: 'test-id',
          status: VideoUploadStatus.uploading,
          progressPercentage: 50.0,
        );

        final updated = original.copyWith(
          status: VideoUploadStatus.failed,
          error: 'Network error',
        );

        expect(updated.videoId, equals('test-id'));
        expect(updated.status, equals(VideoUploadStatus.failed));
        expect(updated.progressPercentage, equals(50.0)); // Unchanged
        expect(updated.error, equals('Network error'));
      });

      test('should create identical copy when no properties updated', () {
        const original = VideoUploadProgress(
          videoId: 'test-id',
          status: VideoUploadStatus.completed,
          progressPercentage: 100.0,
          bytesTransferred: 2000000,
          totalBytes: 2000000,
        );

        final copied = original.copyWith();

        expect(copied, equals(original));
        expect(copied.hashCode, equals(original.hashCode));
      });
    });

    group('Equatable', () {
      test('should be equal when all properties match', () {
        const progress1 = VideoUploadProgress(
          videoId: 'same-id',
          status: VideoUploadStatus.uploading,
          progressPercentage: 75.0,
          bytesTransferred: 1500000,
          totalBytes: 2000000,
        );

        const progress2 = VideoUploadProgress(
          videoId: 'same-id',
          status: VideoUploadStatus.uploading,
          progressPercentage: 75.0,
          bytesTransferred: 1500000,
          totalBytes: 2000000,
        );

        expect(progress1, equals(progress2));
        expect(progress1.hashCode, equals(progress2.hashCode));
      });

      test('should not be equal when properties differ', () {
        const progress1 = VideoUploadProgress(
          videoId: 'id1',
          status: VideoUploadStatus.uploading,
          progressPercentage: 50.0,
        );

        const progress2 = VideoUploadProgress(
          videoId: 'id2',
          status: VideoUploadStatus.uploading,
          progressPercentage: 50.0,
        );

        expect(progress1, isNot(equals(progress2)));
        expect(progress1.hashCode, isNot(equals(progress2.hashCode)));
      });
    });

    group('VideoUploadStatus enum', () {
      test('should have all expected values', () {
        expect(VideoUploadStatus.values, hasLength(4));
        expect(VideoUploadStatus.values, contains(VideoUploadStatus.pending));
        expect(VideoUploadStatus.values, contains(VideoUploadStatus.uploading));
        expect(VideoUploadStatus.values, contains(VideoUploadStatus.completed));
        expect(VideoUploadStatus.values, contains(VideoUploadStatus.failed));
      });

      test('should have correct string representation', () {
        expect(VideoUploadStatus.pending.toString(), equals('VideoUploadStatus.pending'));
        expect(VideoUploadStatus.uploading.toString(), equals('VideoUploadStatus.uploading'));
        expect(VideoUploadStatus.completed.toString(), equals('VideoUploadStatus.completed'));
        expect(VideoUploadStatus.failed.toString(), equals('VideoUploadStatus.failed'));
      });
    });

    group('edge cases', () {
      test('should handle 0% progress', () {
        const progress = VideoUploadProgress(
          videoId: 'test',
          status: VideoUploadStatus.uploading,
          progressPercentage: 0.0,
          bytesTransferred: 0,
          totalBytes: 1000000,
        );

        expect(progress.progressPercentage, equals(0.0));
        expect(progress.isUploading, isTrue);
      });

      test('should handle 100% progress', () {
        const progress = VideoUploadProgress(
          videoId: 'test',
          status: VideoUploadStatus.completed,
          progressPercentage: 100.0,
          bytesTransferred: 1000000,
          totalBytes: 1000000,
        );

        expect(progress.progressPercentage, equals(100.0));
        expect(progress.isCompleted, isTrue);
      });

      test('should handle large file sizes', () {
        const progress = VideoUploadProgress(
          videoId: 'large-file',
          status: VideoUploadStatus.uploading,
          progressPercentage: 33.33,
          bytesTransferred: 1073741824, // 1GB
          totalBytes: 3221225472, // 3GB
        );

        expect(progress.bytesTransferred, equals(1073741824));
        expect(progress.totalBytes, equals(3221225472));
        expect(progress.progressPercentage, equals(33.33));
      });
    });
  });
}
