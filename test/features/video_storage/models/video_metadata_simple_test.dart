import 'package:bloomg_flutter/features/video_storage/models/video_metadata.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('VideoMetadata', () {
    late VideoMetadata videoMetadata;
    late DateTime testTimestamp;

    setUp(() {
      testTimestamp = DateTime(2024, 1, 15, 10, 30);
      videoMetadata = VideoMetadata(
        id: 'test-video-id',
        fileName: 'test_video.mp4',
        uploadTimestamp: testTimestamp,
        qualityScore: 85.5,
        storageRef: 'face_verification_videos/user123/test-video-id',
        thumbnailUrl: 'https://example.com/thumbnail.jpg',
        duration: 9,
        fileSize: 1024000,
        deviceInfo: 'iPhone 15 Pro',
      );
    });

    test('should create VideoMetadata with all properties', () {
      expect(videoMetadata.id, equals('test-video-id'));
      expect(videoMetadata.fileName, equals('test_video.mp4'));
      expect(videoMetadata.uploadTimestamp, equals(testTimestamp));
      expect(videoMetadata.qualityScore, equals(85.5));
      expect(
        videoMetadata.storageRef,
        equals('face_verification_videos/user123/test-video-id'),
      );
      expect(
        videoMetadata.thumbnailUrl,
        equals('https://example.com/thumbnail.jpg'),
      );
      expect(videoMetadata.duration, equals(9));
      expect(videoMetadata.fileSize, equals(1024000));
      expect(videoMetadata.deviceInfo, equals('iPhone 15 Pro'));
    });

    test('should create VideoMetadata with minimal required properties', () {
      final minimalVideo = VideoMetadata(
        id: 'minimal-id',
        fileName: 'minimal.mp4',
        uploadTimestamp: testTimestamp,
        qualityScore: 70,
        storageRef: 'face_verification_videos/user123/minimal-id',
      );

      expect(minimalVideo.id, equals('minimal-id'));
      expect(minimalVideo.fileName, equals('minimal.mp4'));
      expect(minimalVideo.uploadTimestamp, equals(testTimestamp));
      expect(minimalVideo.qualityScore, equals(70.0));
      expect(
        minimalVideo.storageRef,
        equals('face_verification_videos/user123/minimal-id'),
      );
      expect(minimalVideo.thumbnailUrl, isNull);
      expect(minimalVideo.duration, isNull);
      expect(minimalVideo.fileSize, isNull);
      expect(minimalVideo.deviceInfo, isNull);
    });

    group('qualityRating', () {
      test('should return "Excellent" for score >= 80', () {
        final excellentVideo = VideoMetadata(
          id: 'test',
          fileName: 'test.mp4',
          uploadTimestamp: testTimestamp,
          qualityScore: 90,
          storageRef: 'test/ref',
        );
        expect(excellentVideo.qualityRating, equals('Excellent'));
      });

      test('should return "Good" for score >= 70', () {
        final goodVideo = VideoMetadata(
          id: 'test',
          fileName: 'test.mp4',
          uploadTimestamp: testTimestamp,
          qualityScore: 75,
          storageRef: 'test/ref',
        );
        expect(goodVideo.qualityRating, equals('Good'));
      });

      test('should return "Moderate" for score >= 50', () {
        final moderateVideo = VideoMetadata(
          id: 'test',
          fileName: 'test.mp4',
          uploadTimestamp: testTimestamp,
          qualityScore: 60,
          storageRef: 'test/ref',
        );
        expect(moderateVideo.qualityRating, equals('Moderate'));
      });

      test('should return "Poor" for score < 50', () {
        final poorVideo = VideoMetadata(
          id: 'test',
          fileName: 'test.mp4',
          uploadTimestamp: testTimestamp,
          qualityScore: 30,
          storageRef: 'test/ref',
        );
        expect(poorVideo.qualityRating, equals('Poor'));
      });
    });

    group('toFirestore', () {
      test('should convert to Firestore map correctly', () {
        final firestoreMap = videoMetadata.toFirestore();

        // Note: 'id' is not included in toFirestore as it's stored as document ID
        expect(firestoreMap['fileName'], equals('test_video.mp4'));
        expect(firestoreMap['uploadTimestamp'], isA<Timestamp>());
        expect(firestoreMap['qualityScore'], equals(85.5));
        expect(
          firestoreMap['storageRef'],
          equals('face_verification_videos/user123/test-video-id'),
        );
        expect(
          firestoreMap['thumbnailUrl'],
          equals('https://example.com/thumbnail.jpg'),
        );
        expect(firestoreMap['duration'], equals(9));
        expect(firestoreMap['fileSize'], equals(1024000));
        expect(firestoreMap['deviceInfo'], equals('iPhone 15 Pro'));
      });

      test('should handle null optional fields in toFirestore', () {
        final minimalVideo = VideoMetadata(
          id: 'minimal-id',
          fileName: 'minimal.mp4',
          uploadTimestamp: testTimestamp,
          qualityScore: 70,
          storageRef: 'face_verification_videos/user123/minimal-id',
        );

        final firestoreMap = minimalVideo.toFirestore();

        // Note: 'id' is not included in toFirestore as it's stored as document ID
        expect(firestoreMap['fileName'], equals('minimal.mp4'));
        expect(firestoreMap['uploadTimestamp'], isA<Timestamp>());
        expect(firestoreMap['qualityScore'], equals(70.0));
        expect(
          firestoreMap['storageRef'],
          equals('face_verification_videos/user123/minimal-id'),
        );
        expect(firestoreMap['thumbnailUrl'], isNull);
        expect(firestoreMap['duration'], isNull);
        expect(firestoreMap['fileSize'], isNull);
        expect(firestoreMap['deviceInfo'], isNull);
      });
    });

    group('Equatable', () {
      test('should be equal when all properties match', () {
        final video1 = VideoMetadata(
          id: 'same-id',
          fileName: 'same.mp4',
          uploadTimestamp: testTimestamp,
          qualityScore: 80,
          storageRef: 'same/ref',
        );

        final video2 = VideoMetadata(
          id: 'same-id',
          fileName: 'same.mp4',
          uploadTimestamp: testTimestamp,
          qualityScore: 80,
          storageRef: 'same/ref',
        );

        expect(video1, equals(video2));
        expect(video1.hashCode, equals(video2.hashCode));
      });

      test('should not be equal when properties differ', () {
        final video1 = VideoMetadata(
          id: 'id1',
          fileName: 'video1.mp4',
          uploadTimestamp: testTimestamp,
          qualityScore: 80,
          storageRef: 'ref1',
        );

        final video2 = VideoMetadata(
          id: 'id2',
          fileName: 'video2.mp4',
          uploadTimestamp: testTimestamp,
          qualityScore: 80,
          storageRef: 'ref2',
        );

        expect(video1, isNot(equals(video2)));
        expect(video1.hashCode, isNot(equals(video2.hashCode)));
      });
    });

    group('copyWith', () {
      test('should create copy with updated properties', () {
        final updatedVideo = videoMetadata.copyWith(
          qualityScore: 95,
          duration: 15,
        );

        expect(updatedVideo.id, equals(videoMetadata.id));
        expect(updatedVideo.fileName, equals(videoMetadata.fileName));
        expect(
          updatedVideo.uploadTimestamp,
          equals(videoMetadata.uploadTimestamp),
        );
        expect(updatedVideo.qualityScore, equals(95.0)); // Updated
        expect(updatedVideo.storageRef, equals(videoMetadata.storageRef));
        expect(updatedVideo.thumbnailUrl, equals(videoMetadata.thumbnailUrl));
        expect(updatedVideo.duration, equals(15)); // Updated
        expect(updatedVideo.fileSize, equals(videoMetadata.fileSize));
        expect(updatedVideo.deviceInfo, equals(videoMetadata.deviceInfo));
      });

      test('should create identical copy when no properties updated', () {
        final copiedVideo = videoMetadata.copyWith();

        expect(copiedVideo, equals(videoMetadata));
        expect(copiedVideo.hashCode, equals(videoMetadata.hashCode));
      });
    });
  });
}
