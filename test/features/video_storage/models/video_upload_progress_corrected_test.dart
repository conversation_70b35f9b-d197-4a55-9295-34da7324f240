import 'package:bloomg_flutter/features/video_storage/models/video_upload_progress.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('VideoUploadProgress', () {
    test('should create VideoUploadProgress with minimal properties', () {
      const progress = VideoUploadProgress(
        status: VideoUploadStatus.idle,
      );

      expect(progress.status, equals(VideoUploadStatus.idle));
      expect(progress.progressPercentage, equals(0.0));
      expect(progress.bytesTransferred, equals(0));
      expect(progress.totalBytes, equals(0));
      expect(progress.error, isNull);
      expect(progress.videoId, isNull);
    });

    test('should create VideoUploadProgress with all properties', () {
      const progress = VideoUploadProgress(
        status: VideoUploadStatus.uploading,
        progressPercentage: 45.5,
        bytesTransferred: 1024,
        totalBytes: 2048,
        error: 'Test error',
        videoId: 'test-video-id',
      );

      expect(progress.status, equals(VideoUploadStatus.uploading));
      expect(progress.progressPercentage, equals(45.5));
      expect(progress.bytesTransferred, equals(1024));
      expect(progress.totalBytes, equals(2048));
      expect(progress.error, equals('Test error'));
      expect(progress.videoId, equals('test-video-id'));
    });

    group('factory constructors', () {
      test('initial should create idle progress', () {
        final progress = VideoUploadProgress.initial();

        expect(progress.status, equals(VideoUploadStatus.idle));
        expect(progress.progressPercentage, equals(0.0));
        expect(progress.bytesTransferred, equals(0));
        expect(progress.totalBytes, equals(0));
        expect(progress.error, isNull);
        expect(progress.videoId, isNull);
      });

      test('uploading should create uploading progress', () {
        final progress = VideoUploadProgress.uploading(
          progressPercentage: 75.0,
          bytesTransferred: 1536,
          totalBytes: 2048,
          videoId: 'upload-video-id',
        );

        expect(progress.status, equals(VideoUploadStatus.uploading));
        expect(progress.progressPercentage, equals(75.0));
        expect(progress.bytesTransferred, equals(1536));
        expect(progress.totalBytes, equals(2048));
        expect(progress.error, isNull);
        expect(progress.videoId, equals('upload-video-id'));
      });

      test('completed should create completed progress', () {
        final progress = VideoUploadProgress.completed(
          videoId: 'completed-video-id',
        );

        expect(progress.status, equals(VideoUploadStatus.completed));
        expect(progress.progressPercentage, equals(100.0));
        expect(progress.bytesTransferred, equals(0));
        expect(progress.totalBytes, equals(0));
        expect(progress.error, isNull);
        expect(progress.videoId, equals('completed-video-id'));
      });

      test('failed should create failed progress', () {
        final progress = VideoUploadProgress.failed(
          error: 'Upload failed',
          videoId: 'failed-video-id',
        );

        expect(progress.status, equals(VideoUploadStatus.failed));
        expect(progress.progressPercentage, equals(0.0));
        expect(progress.bytesTransferred, equals(0));
        expect(progress.totalBytes, equals(0));
        expect(progress.error, equals('Upload failed'));
        expect(progress.videoId, equals('failed-video-id'));
      });
    });

    group('status checks', () {
      test('isIdle should return true for idle status', () {
        const progress = VideoUploadProgress(
          status: VideoUploadStatus.idle,
        );
        expect(progress.isIdle, isTrue);
        expect(progress.isUploading, isFalse);
        expect(progress.isCompleted, isFalse);
        expect(progress.isFailed, isFalse);
      });

      test('isUploading should return true for uploading status', () {
        const progress = VideoUploadProgress(
          status: VideoUploadStatus.uploading,
        );
        expect(progress.isIdle, isFalse);
        expect(progress.isUploading, isTrue);
        expect(progress.isCompleted, isFalse);
        expect(progress.isFailed, isFalse);
      });

      test('isCompleted should return true for completed status', () {
        const progress = VideoUploadProgress(
          status: VideoUploadStatus.completed,
        );
        expect(progress.isIdle, isFalse);
        expect(progress.isUploading, isFalse);
        expect(progress.isCompleted, isTrue);
        expect(progress.isFailed, isFalse);
      });

      test('isFailed should return true for failed status', () {
        const progress = VideoUploadProgress(
          status: VideoUploadStatus.failed,
        );
        expect(progress.isIdle, isFalse);
        expect(progress.isUploading, isFalse);
        expect(progress.isCompleted, isFalse);
        expect(progress.isFailed, isTrue);
      });
    });

    group('copyWith', () {
      test('should create copy with updated status', () {
        const original = VideoUploadProgress(
          status: VideoUploadStatus.idle,
          progressPercentage: 0.0,
          videoId: 'test-id',
        );

        final updated = original.copyWith(
          status: VideoUploadStatus.uploading,
          progressPercentage: 50.0,
        );

        expect(updated.status, equals(VideoUploadStatus.uploading));
        expect(updated.progressPercentage, equals(50.0));
        expect(updated.videoId, equals('test-id')); // Unchanged
        expect(updated.bytesTransferred, equals(0)); // Unchanged
        expect(updated.totalBytes, equals(0)); // Unchanged
        expect(updated.error, isNull); // Unchanged
      });

      test('should create identical copy when no properties updated', () {
        const original = VideoUploadProgress(
          status: VideoUploadStatus.uploading,
          progressPercentage: 75.0,
          bytesTransferred: 1536,
          totalBytes: 2048,
          videoId: 'test-id',
        );

        final copy = original.copyWith();

        expect(copy, equals(original));
        expect(copy.hashCode, equals(original.hashCode));
      });
    });

    group('formattedProgress', () {
      test('should format progress with bytes when totalBytes > 0', () {
        const progress = VideoUploadProgress(
          status: VideoUploadStatus.uploading,
          progressPercentage: 50.0,
          bytesTransferred: 1024 * 1024, // 1 MB
          totalBytes: 2 * 1024 * 1024, // 2 MB
        );

        expect(progress.formattedProgress, equals('1.0 / 2.0 MB'));
      });

      test('should format progress as percentage when totalBytes is 0', () {
        const progress = VideoUploadProgress(
          status: VideoUploadStatus.uploading,
          progressPercentage: 75.5,
          totalBytes: 0,
        );

        expect(progress.formattedProgress, equals('75.5%'));
      });

      test('should handle large file sizes correctly', () {
        const progress = VideoUploadProgress(
          status: VideoUploadStatus.uploading,
          progressPercentage: 25.0,
          bytesTransferred: 500 * 1024 * 1024, // 500 MB
          totalBytes: 2000 * 1024 * 1024, // 2000 MB (2 GB)
        );

        expect(progress.formattedProgress, equals('500.0 / 2000.0 MB'));
      });
    });

    group('Equatable', () {
      test('should be equal when all properties match', () {
        const progress1 = VideoUploadProgress(
          status: VideoUploadStatus.uploading,
          progressPercentage: 50.0,
          bytesTransferred: 1024,
          totalBytes: 2048,
          videoId: 'same-id',
        );

        const progress2 = VideoUploadProgress(
          status: VideoUploadStatus.uploading,
          progressPercentage: 50.0,
          bytesTransferred: 1024,
          totalBytes: 2048,
          videoId: 'same-id',
        );

        expect(progress1, equals(progress2));
        expect(progress1.hashCode, equals(progress2.hashCode));
      });

      test('should not be equal when properties differ', () {
        const progress1 = VideoUploadProgress(
          status: VideoUploadStatus.uploading,
          progressPercentage: 50.0,
          videoId: 'id1',
        );

        const progress2 = VideoUploadProgress(
          status: VideoUploadStatus.uploading,
          progressPercentage: 75.0,
          videoId: 'id2',
        );

        expect(progress1, isNot(equals(progress2)));
        expect(progress1.hashCode, isNot(equals(progress2.hashCode)));
      });
    });

    group('VideoUploadStatus enum', () {
      test('should contain all expected values', () {
        expect(VideoUploadStatus.values, contains(VideoUploadStatus.idle));
        expect(VideoUploadStatus.values, contains(VideoUploadStatus.uploading));
        expect(VideoUploadStatus.values, contains(VideoUploadStatus.completed));
        expect(VideoUploadStatus.values, contains(VideoUploadStatus.failed));
        expect(VideoUploadStatus.values.length, equals(4));
      });

      test('should have correct string representation', () {
        expect(VideoUploadStatus.idle.toString(), equals('VideoUploadStatus.idle'));
        expect(VideoUploadStatus.uploading.toString(), equals('VideoUploadStatus.uploading'));
        expect(VideoUploadStatus.completed.toString(), equals('VideoUploadStatus.completed'));
        expect(VideoUploadStatus.failed.toString(), equals('VideoUploadStatus.failed'));
      });
    });
  });
}
