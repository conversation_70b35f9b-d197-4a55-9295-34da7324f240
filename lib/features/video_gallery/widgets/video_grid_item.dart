import 'package:flutter/material.dart';
import 'package:bloomg_flutter/features/video_storage/models/video_download_state.dart';
import 'package:bloomg_flutter/features/video_storage/models/video_metadata.dart';

/// {@template video_grid_item}
/// Grid item widget for displaying a video in the gallery.
/// 
/// Shows video thumbnail, metadata, and action buttons.
/// {@endtemplate}
class VideoGridItem extends StatelessWidget {
  /// {@macro video_grid_item}
  const VideoGridItem({
    super.key,
    required this.video,
    required this.downloadState,
    required this.isSelected,
    required this.isPlaying,
    required this.onTap,
    required this.onPlay,
    required this.onDownload,
    required this.onDelete,
  });

  /// Video metadata
  final VideoMetadata video;

  /// Download state for this video
  final VideoDownloadState downloadState;

  /// Whether this video is currently selected
  final bool isSelected;

  /// Whether this video is currently playing
  final bool isPlaying;

  /// Callback when video is tapped
  final VoidCallback onTap;

  /// Callback when play button is pressed
  final VoidCallback onPlay;

  /// Callback when download button is pressed
  final VoidCallback onDownload;

  /// Callback when delete button is pressed
  final VoidCallback onDelete;

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: isSelected ? 8 : 2,
      color: isSelected 
          ? Theme.of(context).primaryColor.withOpacity(0.1)
          : null,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Video thumbnail section
            Expanded(
              flex: 3,
              child: _buildThumbnailSection(context),
            ),
            
            // Video info section
            Expanded(
              flex: 2,
              child: _buildInfoSection(context),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the thumbnail section with play overlay
  Widget _buildThumbnailSection(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
        color: Colors.grey[300],
      ),
      child: Stack(
        children: [
          // Thumbnail placeholder (would be actual thumbnail in real implementation)
          Center(
            child: Icon(
              Icons.video_file,
              size: 48,
              color: Colors.grey[600],
            ),
          ),
          
          // Quality indicator
          Positioned(
            top: 8,
            left: 8,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: _getQualityColor(video.qualityScore),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${video.qualityScore.toStringAsFixed(0)}%',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          
          // Download indicator
          if (downloadState.isDownloaded)
            const Positioned(
              top: 8,
              right: 8,
              child: Icon(
                Icons.download_done,
                color: Colors.green,
                size: 20,
              ),
            )
          else if (downloadState.isDownloading)
            Positioned(
              top: 8,
              right: 8,
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  value: downloadState.progressPercentage / 100,
                  strokeWidth: 2,
                  backgroundColor: Colors.white.withOpacity(0.3),
                  valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
                ),
              ),
            ),
          
          // Play button overlay
          Center(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.7),
                shape: BoxShape.circle,
              ),
              child: IconButton(
                onPressed: onPlay,
                icon: Icon(
                  isPlaying ? Icons.pause : Icons.play_arrow,
                  color: Colors.white,
                  size: 32,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the info section with metadata and actions
  Widget _buildInfoSection(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Quality rating and timestamp
          Row(
            children: [
              Icon(
                _getQualityIcon(video.qualityScore),
                size: 14,
                color: _getQualityColor(video.qualityScore),
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  video.qualityRating,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: _getQualityColor(video.qualityScore),
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 4),
          
          // Upload time
          Text(
            _formatUploadTime(video.uploadTimestamp),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
          
          const Spacer(),
          
          // Action buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Download button
              if (!downloadState.isDownloaded && !downloadState.isDownloading)
                IconButton(
                  onPressed: onDownload,
                  icon: const Icon(Icons.download),
                  iconSize: 20,
                  tooltip: 'Download',
                ),
              
              // Delete button
              IconButton(
                onPressed: onDelete,
                icon: const Icon(Icons.delete_outline),
                iconSize: 20,
                color: Colors.red,
                tooltip: 'Delete',
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Gets color based on quality score
  Color _getQualityColor(double qualityScore) {
    if (qualityScore >= 80) {
      return Colors.green;
    } else if (qualityScore >= 70) {
      return Colors.teal;
    } else if (qualityScore >= 50) {
      return Colors.amber;
    } else {
      return Colors.red;
    }
  }

  /// Gets icon based on quality score
  IconData _getQualityIcon(double qualityScore) {
    if (qualityScore >= 80) {
      return Icons.star;
    } else if (qualityScore >= 70) {
      return Icons.thumb_up;
    } else if (qualityScore >= 50) {
      return Icons.warning;
    } else {
      return Icons.error;
    }
  }

  /// Formats upload timestamp for display
  String _formatUploadTime(DateTime uploadTime) {
    final now = DateTime.now();
    final difference = now.difference(uploadTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Now';
    }
  }
}
