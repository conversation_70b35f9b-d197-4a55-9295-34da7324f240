import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:bloomg_flutter/features/video_gallery/bloc/video_gallery_bloc.dart';
import 'package:bloomg_flutter/features/video_gallery/bloc/video_gallery_event.dart';
import 'package:bloomg_flutter/features/video_gallery/bloc/video_gallery_state.dart';

/// {@template video_gallery_app_bar}
/// Custom app bar for the video gallery screen.
///
/// Provides title, refresh action, and sort/filter options.
/// {@endtemplate}
class VideoGalleryAppBar extends StatelessWidget
    implements PreferredSizeWidget {
  /// {@macro video_gallery_app_bar}
  const VideoGalleryAppBar({super.key});

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<VideoGalleryBloc, VideoGalleryState>(
      builder: (context, state) {
        return AppBar(
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Video Gallery'),
              if (state.hasVideos)
                Text(
                  '${state.filteredVideos.length} of ${state.videos.length} videos',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withOpacity(0.7),
                      ),
                ),
            ],
          ),
          backgroundColor: Theme.of(context).colorScheme.inversePrimary,
          actions: [
            // Refresh button
            IconButton(
              onPressed: state.isLoading || state.isRefreshing
                  ? null
                  : () {
                      context.read<VideoGalleryBloc>().add(
                            const VideoGalleryRefreshed(),
                          );
                    },
              icon: state.isRefreshing
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.refresh),
              tooltip: 'Refresh videos',
            ),

            // Sort menu
            PopupMenuButton<VideoSortField>(
              onSelected: (sortField) {
                final isCurrentField = state.sortBy == sortField;
                final newAscending =
                    isCurrentField ? !state.sortAscending : false;

                context.read<VideoGalleryBloc>().add(
                      VideoGallerySortChanged(
                        sortBy: sortField,
                        ascending: newAscending,
                      ),
                    );
              },
              icon: const Icon(Icons.sort),
              tooltip: 'Sort videos',
              itemBuilder: (context) => [
                _buildSortMenuItem(
                  context,
                  VideoSortField.uploadDate,
                  'Upload Date',
                  Icons.schedule,
                  state,
                ),
                _buildSortMenuItem(
                  context,
                  VideoSortField.qualityScore,
                  'Quality Score',
                  Icons.star,
                  state,
                ),
                _buildSortMenuItem(
                  context,
                  VideoSortField.fileSize,
                  'File Size',
                  Icons.storage,
                  state,
                ),
                _buildSortMenuItem(
                  context,
                  VideoSortField.fileName,
                  'File Name',
                  Icons.abc,
                  state,
                ),
              ],
            ),

            // More options menu
            PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'home':
                    context.go('/');
                  case 'settings':
                    // TODO(dev): Navigate to settings
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Settings coming soon')),
                    );
                }
              },
              icon: const Icon(Icons.more_vert),
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'home',
                  child: ListTile(
                    leading: Icon(Icons.home),
                    title: Text('Home'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'settings',
                  child: ListTile(
                    leading: Icon(Icons.settings),
                    title: Text('Settings'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  /// Builds a sort menu item with current state indication
  PopupMenuItem<VideoSortField> _buildSortMenuItem(
    BuildContext context,
    VideoSortField sortField,
    String title,
    IconData icon,
    VideoGalleryState state,
  ) {
    final isSelected = state.sortBy == sortField;
    final isAscending = state.sortAscending;

    return PopupMenuItem(
      value: sortField,
      child: ListTile(
        leading: Icon(
          icon,
          color: isSelected ? Theme.of(context).primaryColor : null,
        ),
        title: Text(
          title,
          style: TextStyle(
            color: isSelected ? Theme.of(context).primaryColor : null,
            fontWeight: isSelected ? FontWeight.bold : null,
          ),
        ),
        trailing: isSelected
            ? Icon(
                isAscending ? Icons.arrow_upward : Icons.arrow_downward,
                size: 16,
                color: Theme.of(context).primaryColor,
              )
            : null,
        contentPadding: EdgeInsets.zero,
      ),
    );
  }
}
