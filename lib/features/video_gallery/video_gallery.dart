/// Video Gallery Feature
/// 
/// This library provides complete video gallery functionality including:
/// - Grid view of face verification videos
/// - Filtering and sorting capabilities
/// - Video download and local storage
/// - Video playback integration
/// - Video deletion and management
/// 
/// Main components:
/// - [VideoGalleryBloc] - State management for gallery operations
/// - [VideoGalleryScreen] - Main gallery UI with grid layout
/// - [VideoGridItem] - Individual video card component
/// - [VideoGalleryAppBar] - Custom app bar with actions
/// - [VideoGalleryFilterBar] - Filter chips for video categories

library video_gallery;

// BLoC exports
export 'bloc/video_gallery_bloc.dart';
export 'bloc/video_gallery_event.dart';
export 'bloc/video_gallery_state.dart';

// View exports
export 'view/video_gallery_screen.dart';

// Widget exports
export 'widgets/video_gallery_app_bar.dart';
export 'widgets/video_gallery_filter_bar.dart';
export 'widgets/video_grid_item.dart';
