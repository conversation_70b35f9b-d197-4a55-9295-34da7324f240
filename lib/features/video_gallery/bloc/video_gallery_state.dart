import 'package:equatable/equatable.dart';
import 'package:bloomg_flutter/features/video_gallery/bloc/video_gallery_event.dart';
import 'package:bloomg_flutter/features/video_storage/models/video_download_state.dart';
import 'package:bloomg_flutter/features/video_storage/models/video_metadata.dart';

/// {@template video_gallery_status}
/// Enumeration of possible video gallery statuses.
/// {@endtemplate}
enum VideoGalleryStatus {
  /// Initial state - not loaded
  initial,

  /// Loading videos from Firebase Storage
  loading,

  /// Videos loaded successfully
  loaded,

  /// Refreshing video list
  refreshing,

  /// An error occurred
  error,
}

/// {@template video_gallery_state}
/// State for the video gallery BLoC.
/// 
/// Tracks videos, download states, playback status, and filtering options.
/// {@endtemplate}
class VideoGalleryState extends Equatable {
  /// {@macro video_gallery_state}
  const VideoGalleryState({
    this.status = VideoGalleryStatus.initial,
    this.videos = const [],
    this.filteredVideos = const [],
    this.downloadStates = const {},
    this.selectedVideo,
    this.playingVideo,
    this.playbackUrl,
    this.filter = VideoGalleryFilter.all,
    this.sortBy = VideoSortField.uploadDate,
    this.sortAscending = false,
    this.error,
    this.stackTrace,
  });

  /// Current status of the video gallery
  final VideoGalleryStatus status;

  /// All videos from Firebase Storage
  final List<VideoMetadata> videos;

  /// Filtered and sorted videos for display
  final List<VideoMetadata> filteredVideos;

  /// Download states for each video
  final Map<String, VideoDownloadState> downloadStates;

  /// Currently selected video
  final VideoMetadata? selectedVideo;

  /// Currently playing video
  final VideoMetadata? playingVideo;

  /// URL or local path for video playback
  final String? playbackUrl;

  /// Current filter applied to videos
  final VideoGalleryFilter filter;

  /// Field to sort videos by
  final VideoSortField sortBy;

  /// Whether to sort in ascending order
  final bool sortAscending;

  /// Error message if an error occurred
  final Object? error;

  /// Stack trace if an error occurred
  final StackTrace? stackTrace;

  /// Creates a copy of this state with updated fields
  VideoGalleryState copyWith({
    VideoGalleryStatus? status,
    List<VideoMetadata>? videos,
    List<VideoMetadata>? filteredVideos,
    Map<String, VideoDownloadState>? downloadStates,
    VideoMetadata? selectedVideo,
    VideoMetadata? playingVideo,
    String? playbackUrl,
    VideoGalleryFilter? filter,
    VideoSortField? sortBy,
    bool? sortAscending,
    Object? error,
    StackTrace? stackTrace,
  }) {
    return VideoGalleryState(
      status: status ?? this.status,
      videos: videos ?? this.videos,
      filteredVideos: filteredVideos ?? this.filteredVideos,
      downloadStates: downloadStates ?? this.downloadStates,
      selectedVideo: selectedVideo ?? this.selectedVideo,
      playingVideo: playingVideo ?? this.playingVideo,
      playbackUrl: playbackUrl ?? this.playbackUrl,
      filter: filter ?? this.filter,
      sortBy: sortBy ?? this.sortBy,
      sortAscending: sortAscending ?? this.sortAscending,
      error: error ?? this.error,
      stackTrace: stackTrace ?? this.stackTrace,
    );
  }

  /// Creates an initial state
  factory VideoGalleryState.initial() {
    return const VideoGalleryState();
  }

  /// Creates a loading state
  factory VideoGalleryState.loading() {
    return const VideoGalleryState(status: VideoGalleryStatus.loading);
  }

  /// Creates a loaded state
  factory VideoGalleryState.loaded({
    required List<VideoMetadata> videos,
    required List<VideoMetadata> filteredVideos,
    VideoGalleryFilter filter = VideoGalleryFilter.all,
    VideoSortField sortBy = VideoSortField.uploadDate,
    bool sortAscending = false,
  }) {
    return VideoGalleryState(
      status: VideoGalleryStatus.loaded,
      videos: videos,
      filteredVideos: filteredVideos,
      filter: filter,
      sortBy: sortBy,
      sortAscending: sortAscending,
    );
  }

  /// Creates a refreshing state
  factory VideoGalleryState.refreshing({
    required List<VideoMetadata> videos,
    required List<VideoMetadata> filteredVideos,
    VideoGalleryFilter filter = VideoGalleryFilter.all,
    VideoSortField sortBy = VideoSortField.uploadDate,
    bool sortAscending = false,
  }) {
    return VideoGalleryState(
      status: VideoGalleryStatus.refreshing,
      videos: videos,
      filteredVideos: filteredVideos,
      filter: filter,
      sortBy: sortBy,
      sortAscending: sortAscending,
    );
  }

  /// Creates an error state
  factory VideoGalleryState.error({
    required Object error,
    StackTrace? stackTrace,
    List<VideoMetadata>? videos,
    List<VideoMetadata>? filteredVideos,
  }) {
    return VideoGalleryState(
      status: VideoGalleryStatus.error,
      videos: videos ?? const [],
      filteredVideos: filteredVideos ?? const [],
      error: error,
      stackTrace: stackTrace,
    );
  }

  /// Whether the gallery is currently loading
  bool get isLoading => status == VideoGalleryStatus.loading;

  /// Whether the gallery is loaded
  bool get isLoaded => status == VideoGalleryStatus.loaded;

  /// Whether the gallery is refreshing
  bool get isRefreshing => status == VideoGalleryStatus.refreshing;

  /// Whether an error occurred
  bool get hasError => status == VideoGalleryStatus.error;

  /// Whether there are any videos
  bool get hasVideos => videos.isNotEmpty;

  /// Whether there are filtered videos to display
  bool get hasFilteredVideos => filteredVideos.isNotEmpty;

  /// Whether a video is currently playing
  bool get isPlaying => playingVideo != null;

  /// Gets download state for a specific video
  VideoDownloadState getDownloadState(String videoId) {
    return downloadStates[videoId] ?? VideoDownloadState.initial();
  }

  /// Whether a specific video is downloaded
  bool isVideoDownloaded(String videoId) {
    return getDownloadState(videoId).isDownloaded;
  }

  /// Whether a specific video is downloading
  bool isVideoDownloading(String videoId) {
    return getDownloadState(videoId).isDownloading;
  }

  /// Gets the local path for a downloaded video
  String? getLocalPath(String videoId) {
    return getDownloadState(videoId).localFilePath;
  }

  /// Gets the download progress for a video
  double getDownloadProgress(String videoId) {
    return getDownloadState(videoId).progressPercentage;
  }

  /// Gets display text for current filter
  String get filterDisplayText {
    switch (filter) {
      case VideoGalleryFilter.all:
        return 'All Videos';
      case VideoGalleryFilter.highQuality:
        return 'High Quality (80%+)';
      case VideoGalleryFilter.goodQuality:
        return 'Good Quality (70%+)';
      case VideoGalleryFilter.recent:
        return 'Recent (7 days)';
    }
  }

  /// Gets display text for current sort
  String get sortDisplayText {
    final field = switch (sortBy) {
      VideoSortField.uploadDate => 'Upload Date',
      VideoSortField.qualityScore => 'Quality Score',
      VideoSortField.fileSize => 'File Size',
      VideoSortField.fileName => 'File Name',
    };
    
    final order = sortAscending ? 'Ascending' : 'Descending';
    return '$field ($order)';
  }

  @override
  List<Object?> get props => [
        status,
        videos,
        filteredVideos,
        downloadStates,
        selectedVideo,
        playingVideo,
        playbackUrl,
        filter,
        sortBy,
        sortAscending,
        error,
        stackTrace,
      ];
}
