import 'package:equatable/equatable.dart';
import 'package:bloomg_flutter/features/video_storage/models/video_metadata.dart';

/// {@template video_gallery_event}
/// Base class for all video gallery events.
/// {@endtemplate}
abstract class VideoGalleryEvent extends Equatable {
  /// {@macro video_gallery_event}
  const VideoGalleryEvent();

  @override
  List<Object?> get props => [];
}

/// {@template video_gallery_loaded}
/// Event triggered to load videos from Firebase Storage.
/// {@endtemplate}
class VideoGalleryLoaded extends VideoGalleryEvent {
  /// {@macro video_gallery_loaded}
  const VideoGalleryLoaded();
}

/// {@template video_gallery_refreshed}
/// Event triggered to refresh the video gallery.
/// {@endtemplate}
class VideoGalleryRefreshed extends VideoGalleryEvent {
  /// {@macro video_gallery_refreshed}
  const VideoGalleryRefreshed();
}

/// {@template video_selected}
/// Event triggered when a video is selected for playback.
/// {@endtemplate}
class VideoSelected extends VideoGalleryEvent {
  /// {@macro video_selected}
  const VideoSelected({
    required this.video,
  });

  /// The selected video metadata
  final VideoMetadata video;

  @override
  List<Object?> get props => [video];
}

/// {@template video_download_started}
/// Event triggered when video download for offline playback starts.
/// {@endtemplate}
class VideoDownloadStarted extends VideoGalleryEvent {
  /// {@macro video_download_started}
  const VideoDownloadStarted({
    required this.video,
  });

  /// The video to download
  final VideoMetadata video;

  @override
  List<Object?> get props => [video];
}

/// {@template video_download_completed}
/// Event triggered when video download is completed.
/// {@endtemplate}
class VideoDownloadCompleted extends VideoGalleryEvent {
  /// {@macro video_download_completed}
  const VideoDownloadCompleted({
    required this.video,
    required this.localPath,
  });

  /// The downloaded video metadata
  final VideoMetadata video;

  /// Local file path of the downloaded video
  final String localPath;

  @override
  List<Object?> get props => [video, localPath];
}

/// {@template video_playback_started}
/// Event triggered when video playback starts.
/// {@endtemplate}
class VideoPlaybackStarted extends VideoGalleryEvent {
  /// {@macro video_playback_started}
  const VideoPlaybackStarted({
    required this.video,
    required this.videoUrl,
  });

  /// The video being played
  final VideoMetadata video;

  /// URL or local path for video playback
  final String videoUrl;

  @override
  List<Object?> get props => [video, videoUrl];
}

/// {@template video_playback_stopped}
/// Event triggered when video playback stops.
/// {@endtemplate}
class VideoPlaybackStopped extends VideoGalleryEvent {
  /// {@macro video_playback_stopped}
  const VideoPlaybackStopped();
}

/// {@template video_deleted}
/// Event triggered when a video is deleted.
/// {@endtemplate}
class VideoDeleted extends VideoGalleryEvent {
  /// {@macro video_deleted}
  const VideoDeleted({
    required this.video,
  });

  /// The video to delete
  final VideoMetadata video;

  @override
  List<Object?> get props => [video];
}

/// {@template video_gallery_error}
/// Event triggered when an error occurs in the video gallery.
/// {@endtemplate}
class VideoGalleryError extends VideoGalleryEvent {
  /// {@macro video_gallery_error}
  const VideoGalleryError({
    required this.error,
    this.stackTrace,
  });

  /// Error message or object
  final Object error;

  /// Optional stack trace
  final StackTrace? stackTrace;

  @override
  List<Object?> get props => [error, stackTrace];
}

/// {@template video_gallery_filter_changed}
/// Event triggered when the gallery filter is changed.
/// {@endtemplate}
class VideoGalleryFilterChanged extends VideoGalleryEvent {
  /// {@macro video_gallery_filter_changed}
  const VideoGalleryFilterChanged({
    required this.filter,
  });

  /// The new filter to apply
  final VideoGalleryFilter filter;

  @override
  List<Object?> get props => [filter];
}

/// {@template video_gallery_sort_changed}
/// Event triggered when the gallery sort order is changed.
/// {@endtemplate}
class VideoGallerySortChanged extends VideoGalleryEvent {
  /// {@macro video_gallery_sort_changed}
  const VideoGallerySortChanged({
    required this.sortBy,
    required this.ascending,
  });

  /// Field to sort by
  final VideoSortField sortBy;

  /// Whether to sort in ascending order
  final bool ascending;

  @override
  List<Object?> get props => [sortBy, ascending];
}

/// Enumeration of video gallery filters
enum VideoGalleryFilter {
  /// Show all videos
  all,

  /// Show only high quality videos (80%+)
  highQuality,

  /// Show only good quality videos (70%+)
  goodQuality,

  /// Show only recent videos (last 7 days)
  recent,
}

/// Enumeration of video sort fields
enum VideoSortField {
  /// Sort by upload timestamp
  uploadDate,

  /// Sort by quality score
  qualityScore,

  /// Sort by file size
  fileSize,

  /// Sort by file name
  fileName,
}
