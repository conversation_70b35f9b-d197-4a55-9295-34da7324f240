import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:bloomg_flutter/features/video_gallery/bloc/video_gallery_bloc.dart';
import 'package:bloomg_flutter/features/video_gallery/bloc/video_gallery_event.dart';
import 'package:bloomg_flutter/features/video_gallery/bloc/video_gallery_state.dart';
import 'package:bloomg_flutter/features/video_gallery/widgets/video_grid_item.dart';
import 'package:bloomg_flutter/features/video_gallery/widgets/video_gallery_app_bar.dart';
import 'package:bloomg_flutter/features/video_gallery/widgets/video_gallery_filter_bar.dart';
import 'package:bloomg_flutter/features/video_storage/models/video_metadata.dart';

/// {@template video_gallery_screen}
/// Screen for displaying and managing face verification videos.
///
/// Provides a grid view of videos with filtering, sorting, and playback options.
/// {@endtemplate}
class VideoGalleryScreen extends StatelessWidget {
  /// {@macro video_gallery_screen}
  const VideoGalleryScreen({super.key});

  /// Route name for navigation
  static const String routeName = '/video-gallery';

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => VideoGalleryBloc()..add(const VideoGalleryLoaded()),
      child: const VideoGalleryView(),
    );
  }
}

/// {@template video_gallery_view}
/// Main view widget for video gallery screen.
/// {@endtemplate}
class VideoGalleryView extends StatelessWidget {
  /// {@macro video_gallery_view}
  const VideoGalleryView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const VideoGalleryAppBar(),
      body: BlocConsumer<VideoGalleryBloc, VideoGalleryState>(
        listener: (context, state) {
          if (state.hasError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Error: ${state.error}'),
                backgroundColor: Colors.red,
                action: SnackBarAction(
                  label: 'Retry',
                  onPressed: () {
                    context
                        .read<VideoGalleryBloc>()
                        .add(const VideoGalleryRefreshed());
                  },
                ),
              ),
            );
          }
        },
        builder: (context, state) {
          return Column(
            children: [
              const VideoGalleryFilterBar(),
              Expanded(
                child: _buildContent(context, state),
              ),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => context.go('/face-verification'),
        icon: const Icon(Icons.face),
        label: const Text('Record Video'),
        tooltip: 'Record new face verification video',
      ),
    );
  }

  /// Builds the main content based on current state
  Widget _buildContent(BuildContext context, VideoGalleryState state) {
    if (state.isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading videos...'),
          ],
        ),
      );
    }

    if (state.hasError && !state.hasVideos) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to load videos',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              '${state.error}',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                context
                    .read<VideoGalleryBloc>()
                    .add(const VideoGalleryRefreshed());
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (!state.hasFilteredVideos) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.video_library_outlined,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              state.hasVideos ? 'No videos match your filter' : 'No videos yet',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              state.hasVideos
                  ? 'Try changing your filter or sort options'
                  : 'Record your first face verification video',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            if (state.hasVideos)
              OutlinedButton(
                onPressed: () {
                  context.read<VideoGalleryBloc>().add(
                        const VideoGalleryFilterChanged(
                          filter: VideoGalleryFilter.all,
                        ),
                      );
                },
                child: const Text('Show All Videos'),
              )
            else
              ElevatedButton(
                onPressed: () {
                  Navigator.pushNamed(context, '/face-verification');
                },
                child: const Text('Record Video'),
              ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        context.read<VideoGalleryBloc>().add(const VideoGalleryRefreshed());
        // Wait for refresh to complete
        await Future<void>.delayed(const Duration(milliseconds: 500));
      },
      child: CustomScrollView(
        slivers: [
          if (state.isRefreshing)
            const SliverToBoxAdapter(
              child: LinearProgressIndicator(),
            ),
          SliverPadding(
            padding: const EdgeInsets.all(16),
            sliver: SliverGrid(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: 0.75,
              ),
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final video = state.filteredVideos[index];
                  return VideoGridItem(
                    video: video,
                    downloadState: state.getDownloadState(video.id),
                    isSelected: state.selectedVideo?.id == video.id,
                    isPlaying: state.playingVideo?.id == video.id,
                    onTap: () {
                      context.read<VideoGalleryBloc>().add(
                            VideoSelected(video: video),
                          );
                    },
                    onPlay: () {
                      // Navigate to video player screen
                      context.go('/video-player/${video.id}');

                      context.read<VideoGalleryBloc>().add(
                            VideoPlaybackStarted(
                              video: video,
                              videoUrl: video.storageRef,
                            ),
                          );
                    },
                    onDownload: () {
                      context.read<VideoGalleryBloc>().add(
                            VideoDownloadStarted(video: video),
                          );
                    },
                    onDelete: () {
                      _showDeleteConfirmation(context, video);
                    },
                  );
                },
                childCount: state.filteredVideos.length,
              ),
            ),
          ),
          // Add some bottom padding for the FAB
          const SliverToBoxAdapter(
            child: SizedBox(height: 80),
          ),
        ],
      ),
    );
  }

  /// Shows confirmation dialog for video deletion
  void _showDeleteConfirmation(BuildContext context, VideoMetadata video) {
    showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Video'),
        content: Text(
          'Are you sure you want to delete this video?\n\n'
          'Quality: ${video.qualityScore.toStringAsFixed(1)}%\n'
          'Uploaded: ${_formatDate(video.uploadTimestamp)}',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(true);
              context.read<VideoGalleryBloc>().add(
                    VideoDeleted(video: video),
                  );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  /// Formats a DateTime for display
  String _formatDate(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'} ago';
    } else {
      return 'Just now';
    }
  }
}
