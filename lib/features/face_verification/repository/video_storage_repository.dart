import 'dart:async';
import 'dart:io';

import 'package:bloomg_flutter/features/video_storage/models/video_metadata.dart';
import 'package:bloomg_flutter/features/video_storage/models/video_upload_progress.dart';
import 'package:bloomg_flutter/features/video_storage/repository/firebase_video_storage_repository.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:camerawesome/camerawesome_plugin.dart';
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';

/// {@template video_storage_repository}
/// Repository for managing video recording and storage operations.
/// 
/// Handles local video recording with CamerAwesome and uploads to Firebase Storage.
/// Provides a bridge between the face verification BLoC and Firebase Storage.
/// {@endtemplate}
class VideoStorageRepository {
  /// {@macro video_storage_repository}
  VideoStorageRepository({
    FirebaseVideoStorageRepository? firebaseRepository,
  }) : _firebaseRepository = firebaseRepository ?? FirebaseVideoStorageRepository();

  final FirebaseVideoStorageRepository _firebaseRepository;
  final LoggerService _logger = LoggerService();
  final Uuid _uuid = const Uuid();

  // Current recording state
  String? _currentVideoPath;
  bool _isRecording = false;
  CameraAwesomeBuilder? _cameraController;

  /// Stream of upload progress updates
  Stream<VideoUploadProgress> get uploadProgress => 
      _firebaseRepository.uploadProgress;

  /// Initializes the video storage repository
  Future<void> initialize() async {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.videoStorageModule,
        'Repository initialized',
        'Ready for video operations',
      ),
    );
  }

  /// Starts video recording
  Future<void> startRecording() async {
    if (_isRecording) {
      throw Exception('Recording is already in progress');
    }

    try {
      // Generate unique filename for this recording
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final videoId = _uuid.v4();
      final fileName = 'face_verification_${timestamp}_$videoId.mp4';
      
      // Get temporary directory for recording
      final tempDir = await getTemporaryDirectory();
      _currentVideoPath = '${tempDir.path}/$fileName';

      _isRecording = true;

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.videoStorageModule,
          'Recording started',
          'Path: $_currentVideoPath',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoStorageModule,
          LoggingConstants.criticalError,
          'Failed to start recording: $error',
        ),
        error,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Stops video recording and returns the local file path
  Future<String> stopRecording() async {
    if (!_isRecording) {
      throw Exception('No recording in progress');
    }

    try {
      _isRecording = false;
      
      final videoPath = _currentVideoPath;
      if (videoPath == null) {
        throw Exception('No video path available');
      }

      // Verify the file exists
      final file = File(videoPath);
      if (!file.existsSync()) {
        throw Exception('Recorded video file not found: $videoPath');
      }

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.videoStorageModule,
          'Recording stopped',
          'Path: $videoPath, Size: ${file.lengthSync()} bytes',
        ),
      );

      return videoPath;
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoStorageModule,
          LoggingConstants.criticalError,
          'Failed to stop recording: $error',
        ),
        error,
        stackTrace,
      );
      rethrow;
    } finally {
      _currentVideoPath = null;
    }
  }

  /// Uploads a recorded video to Firebase Storage
  Future<VideoMetadata> uploadVideo({
    required String localFilePath,
    required double qualityScore,
    String? deviceInfo,
  }) async {
    try {
      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.videoStorageModule,
          'Upload initiated',
          'File: $localFilePath, Quality: ${qualityScore.toStringAsFixed(1)}%',
        ),
      );

      final metadata = await _firebaseRepository.uploadVideo(
        localFilePath: localFilePath,
        qualityScore: qualityScore,
        deviceInfo: deviceInfo,
      );

      // Clean up local file after successful upload
      final file = File(localFilePath);
      if (file.existsSync()) {
        await file.delete();
        _logger.info(
          LoggingConstants.formatMessage(
            LoggingConstants.videoStorageModule,
            'Local file cleaned up',
            'Path: $localFilePath',
          ),
        );
      }

      return metadata;
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoStorageModule,
          LoggingConstants.criticalError,
          'Video upload failed: $error',
          'File: $localFilePath',
        ),
        error,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Gets all videos for the current user
  Future<List<VideoMetadata>> getUserVideos() async {
    return _firebaseRepository.getUserVideos();
  }

  /// Gets a stream of user videos
  Stream<List<VideoMetadata>> getUserVideosStream() {
    return _firebaseRepository.getUserVideosStream();
  }

  /// Gets download URL for a video
  Future<String> getVideoDownloadUrl(String storageRef) async {
    return _firebaseRepository.getVideoDownloadUrl(storageRef);
  }

  /// Downloads a video to local storage
  Future<String> downloadVideoToLocal(VideoMetadata metadata) async {
    return _firebaseRepository.downloadVideoToLocal(metadata);
  }

  /// Deletes a video and its metadata
  Future<void> deleteVideo(VideoMetadata metadata) async {
    return _firebaseRepository.deleteVideo(metadata);
  }

  /// Whether a recording is currently in progress
  bool get isRecording => _isRecording;

  /// Current video path (if recording)
  String? get currentVideoPath => _currentVideoPath;

  /// Disposes resources
  void dispose() {
    _firebaseRepository.dispose();
  }
}
