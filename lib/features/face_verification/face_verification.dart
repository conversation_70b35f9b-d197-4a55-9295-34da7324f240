/// Face Verification Feature
/// 
/// This library provides complete face verification functionality including:
/// - Video recording with face detection
/// - Quality validation and scoring
/// - Firebase Storage upload integration
/// - Real-time progress tracking
/// 
/// Main components:
/// - [FaceVerificationBloc] - State management for verification process
/// - [FaceVerificationScreen] - UI for face verification flow
/// - [VideoStorageRepository] - Repository for video operations
/// - [VideoCaptureConfig] - Configuration for recording parameters

library face_verification;

// BLoC exports
export 'bloc/face_verification_bloc.dart';
export 'bloc/face_verification_event.dart';
export 'bloc/face_verification_state.dart';

// Model exports
export 'models/face_coverage_stats.dart';
export 'models/face_detection_result.dart';
export 'models/video_capture_config.dart';

// Repository exports
export 'repository/video_storage_repository.dart';

// View exports
export 'view/face_verification_screen.dart';
