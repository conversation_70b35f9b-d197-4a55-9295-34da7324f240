import 'package:equatable/equatable.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_coverage_stats.dart';
import 'package:bloomg_flutter/features/face_verification/models/video_capture_config.dart';
import 'package:bloomg_flutter/features/video_storage/models/video_metadata.dart';

/// {@template face_verification_status}
/// Enumeration of possible face verification statuses.
/// {@endtemplate}
enum FaceVerificationStatus {
  /// Initial state - not started
  initial,

  /// Countdown before recording starts
  countdown,

  /// Currently recording video
  recording,

  /// Recording completed, validating video quality
  validating,

  /// Video validation completed
  validated,

  /// Uploading video to Firebase Storage
  uploading,

  /// Upload completed successfully
  completed,

  /// An error occurred during the process
  error,
}

/// {@template face_verification_state}
/// State for the face verification BLoC.
/// 
/// Tracks the current status, progress, and results of the face verification process.
/// {@endtemplate}
class FaceVerificationState extends Equatable {
  /// {@macro face_verification_state}
  const FaceVerificationState({
    this.status = FaceVerificationStatus.initial,
    this.config = const VideoCaptureConfig(),
    this.countdownSeconds = 0,
    this.recordingProgress = 0.0,
    this.uploadProgress = 0.0,
    this.currentCoveragePercentage = 0.0,
    this.faceDetected = false,
    this.coverageStats,
    this.qualityScore = 0.0,
    this.meetsQualityThreshold = false,
    this.videoPath,
    this.uploadedVideo,
    this.error,
    this.stackTrace,
  });

  /// Current status of the face verification process
  final FaceVerificationStatus status;

  /// Configuration for video capture
  final VideoCaptureConfig config;

  /// Countdown seconds remaining before recording starts
  final int countdownSeconds;

  /// Recording progress as a percentage (0-100)
  final double recordingProgress;

  /// Upload progress as a percentage (0-100)
  final double uploadProgress;

  /// Current face coverage percentage in the frame
  final double currentCoveragePercentage;

  /// Whether a face is currently detected
  final bool faceDetected;

  /// Face coverage statistics for the recording
  final FaceCoverageStats? coverageStats;

  /// Overall quality score of the recorded video
  final double qualityScore;

  /// Whether the video meets the quality threshold
  final bool meetsQualityThreshold;

  /// Path to the recorded video file
  final String? videoPath;

  /// Metadata of the uploaded video
  final VideoMetadata? uploadedVideo;

  /// Error message if an error occurred
  final Object? error;

  /// Stack trace if an error occurred
  final StackTrace? stackTrace;

  /// Creates a copy of this state with updated fields
  FaceVerificationState copyWith({
    FaceVerificationStatus? status,
    VideoCaptureConfig? config,
    int? countdownSeconds,
    double? recordingProgress,
    double? uploadProgress,
    double? currentCoveragePercentage,
    bool? faceDetected,
    FaceCoverageStats? coverageStats,
    double? qualityScore,
    bool? meetsQualityThreshold,
    String? videoPath,
    VideoMetadata? uploadedVideo,
    Object? error,
    StackTrace? stackTrace,
  }) {
    return FaceVerificationState(
      status: status ?? this.status,
      config: config ?? this.config,
      countdownSeconds: countdownSeconds ?? this.countdownSeconds,
      recordingProgress: recordingProgress ?? this.recordingProgress,
      uploadProgress: uploadProgress ?? this.uploadProgress,
      currentCoveragePercentage: currentCoveragePercentage ?? this.currentCoveragePercentage,
      faceDetected: faceDetected ?? this.faceDetected,
      coverageStats: coverageStats ?? this.coverageStats,
      qualityScore: qualityScore ?? this.qualityScore,
      meetsQualityThreshold: meetsQualityThreshold ?? this.meetsQualityThreshold,
      videoPath: videoPath ?? this.videoPath,
      uploadedVideo: uploadedVideo ?? this.uploadedVideo,
      error: error ?? this.error,
      stackTrace: stackTrace ?? this.stackTrace,
    );
  }

  /// Creates an initial state
  factory FaceVerificationState.initial() {
    return const FaceVerificationState();
  }

  /// Creates a countdown state
  factory FaceVerificationState.countdown({
    required VideoCaptureConfig config,
    required int countdownSeconds,
  }) {
    return FaceVerificationState(
      status: FaceVerificationStatus.countdown,
      config: config,
      countdownSeconds: countdownSeconds,
    );
  }

  /// Creates a recording state
  factory FaceVerificationState.recording({
    required VideoCaptureConfig config,
    required double recordingProgress,
    required double currentCoveragePercentage,
    required bool faceDetected,
  }) {
    return FaceVerificationState(
      status: FaceVerificationStatus.recording,
      config: config,
      recordingProgress: recordingProgress,
      currentCoveragePercentage: currentCoveragePercentage,
      faceDetected: faceDetected,
    );
  }

  /// Creates a validating state
  factory FaceVerificationState.validating({
    required VideoCaptureConfig config,
    required String videoPath,
  }) {
    return FaceVerificationState(
      status: FaceVerificationStatus.validating,
      config: config,
      videoPath: videoPath,
    );
  }

  /// Creates a validated state
  factory FaceVerificationState.validated({
    required VideoCaptureConfig config,
    required String videoPath,
    required FaceCoverageStats coverageStats,
    required double qualityScore,
    required bool meetsQualityThreshold,
  }) {
    return FaceVerificationState(
      status: FaceVerificationStatus.validated,
      config: config,
      videoPath: videoPath,
      coverageStats: coverageStats,
      qualityScore: qualityScore,
      meetsQualityThreshold: meetsQualityThreshold,
    );
  }

  /// Creates an uploading state
  factory FaceVerificationState.uploading({
    required VideoCaptureConfig config,
    required String videoPath,
    required double qualityScore,
    required double uploadProgress,
  }) {
    return FaceVerificationState(
      status: FaceVerificationStatus.uploading,
      config: config,
      videoPath: videoPath,
      qualityScore: qualityScore,
      uploadProgress: uploadProgress,
    );
  }

  /// Creates a completed state
  factory FaceVerificationState.completed({
    required VideoCaptureConfig config,
    required VideoMetadata uploadedVideo,
    required double qualityScore,
  }) {
    return FaceVerificationState(
      status: FaceVerificationStatus.completed,
      config: config,
      uploadedVideo: uploadedVideo,
      qualityScore: qualityScore,
      meetsQualityThreshold: true,
    );
  }

  /// Creates an error state
  factory FaceVerificationState.error({
    required Object error,
    StackTrace? stackTrace,
    VideoCaptureConfig? config,
  }) {
    return FaceVerificationState(
      status: FaceVerificationStatus.error,
      config: config ?? const VideoCaptureConfig(),
      error: error,
      stackTrace: stackTrace,
    );
  }

  /// Whether the verification process is in progress
  bool get isInProgress => status == FaceVerificationStatus.countdown ||
      status == FaceVerificationStatus.recording ||
      status == FaceVerificationStatus.validating ||
      status == FaceVerificationStatus.uploading;

  /// Whether the verification process is completed successfully
  bool get isCompleted => status == FaceVerificationStatus.completed;

  /// Whether an error occurred
  bool get hasError => status == FaceVerificationStatus.error;

  /// Whether recording is currently active
  bool get isRecording => status == FaceVerificationStatus.recording;

  /// Whether countdown is active
  bool get isCountdown => status == FaceVerificationStatus.countdown;

  /// Whether video is being uploaded
  bool get isUploading => status == FaceVerificationStatus.uploading;

  /// Quality rating based on score
  String get qualityRating {
    if (qualityScore >= 80) return 'Excellent';
    if (qualityScore >= 70) return 'Good';
    if (qualityScore >= 50) return 'Moderate';
    return 'Poor';
  }

  @override
  List<Object?> get props => [
        status,
        config,
        countdownSeconds,
        recordingProgress,
        uploadProgress,
        currentCoveragePercentage,
        faceDetected,
        coverageStats,
        qualityScore,
        meetsQualityThreshold,
        videoPath,
        uploadedVideo,
        error,
        stackTrace,
      ];
}
