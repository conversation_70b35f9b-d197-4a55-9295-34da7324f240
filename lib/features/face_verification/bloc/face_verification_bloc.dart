import 'dart:async';
import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:bloomg_flutter/features/face_verification/bloc/face_verification_event.dart';
import 'package:bloomg_flutter/features/face_verification/bloc/face_verification_state.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_coverage_stats.dart';
import 'package:bloomg_flutter/features/face_verification/models/video_capture_config.dart';
import 'package:bloomg_flutter/features/face_verification/repository/video_storage_repository.dart';
import 'package:bloomg_flutter/features/video_storage/models/video_upload_progress.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';

/// {@template face_verification_bloc}
/// BLoC for managing face verification video recording and upload process.
///
/// Handles the complete flow from countdown to recording, validation, and upload.
/// {@endtemplate}
class FaceVerificationBloc
    extends Bloc<FaceVerificationEvent, FaceVerificationState> {
  /// {@macro face_verification_bloc}
  FaceVerificationBloc({
    VideoStorageRepository? videoStorageRepository,
  })  : _videoStorageRepository =
            videoStorageRepository ?? VideoStorageRepository(),
        super(FaceVerificationState.initial()) {
    // Register event handlers
    on<FaceVerificationStarted>(_onFaceVerificationStarted);
    on<RecordingCountdownStarted>(_onRecordingCountdownStarted);
    on<RecordingStarted>(_onRecordingStarted);
    on<FaceDetectionFrameProcessed>(_onFaceDetectionFrameProcessed);
    on<RecordingCompleted>(_onRecordingCompleted);
    on<VideoValidationStarted>(_onVideoValidationStarted);
    on<VideoValidationCompleted>(_onVideoValidationCompleted);
    on<VideoUploadStarted>(_onVideoUploadStarted);
    on<VideoUploadProgressUpdated>(_onVideoUploadProgressUpdated);
    on<VideoUploadCompleted>(_onVideoUploadCompleted);
    on<FaceVerificationError>(_onFaceVerificationError);
    on<FaceVerificationReset>(_onFaceVerificationReset);
    on<RetryFaceVerification>(_onRetryFaceVerification);

    // Initialize repository
    _videoStorageRepository.initialize();

    // Listen to upload progress
    _uploadProgressSubscription = _videoStorageRepository.uploadProgress.listen(
      (progress) {
        if (progress.status == VideoUploadStatus.uploading) {
          add(
            VideoUploadProgressUpdated(
              progressPercentage: progress.progressPercentage,
            ),
          );
        } else if (progress.status == VideoUploadStatus.completed) {
          add(VideoUploadCompleted(videoId: progress.videoId ?? 'unknown'));
        } else if (progress.status == VideoUploadStatus.failed) {
          add(FaceVerificationError(error: progress.error ?? 'Upload failed'));
        }
      },
      onError: (Object error) {
        add(FaceVerificationError(error: error));
      },
    );
  }

  final VideoStorageRepository _videoStorageRepository;
  final LoggerService _logger = LoggerService();

  StreamSubscription<VideoUploadProgress>? _uploadProgressSubscription;
  Timer? _countdownTimer;
  Timer? _recordingTimer;
  Timer? _frameProcessingTimer;

  // Face detection tracking
  final List<double> _coverageHistory = [];
  double _currentCoverage = 0;
  bool _currentFaceDetected = false;

  /// Handles face verification started event
  Future<void> _onFaceVerificationStarted(
    FaceVerificationStarted event,
    Emitter<FaceVerificationState> emit,
  ) async {
    try {
      final config = event.config ?? const VideoCaptureConfig();

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Verification started',
          'Config: $config',
        ),
      );

      // Start countdown
      add(const RecordingCountdownStarted());
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.criticalError,
          'Failed to start verification: $error',
          'Event: FaceVerificationStarted',
        ),
        error,
        stackTrace,
      );

      add(FaceVerificationError(error: error, stackTrace: stackTrace));
    }
  }

  /// Handles recording countdown started event
  Future<void> _onRecordingCountdownStarted(
    RecordingCountdownStarted event,
    Emitter<FaceVerificationState> emit,
  ) async {
    final config = state.config;
    var countdownSeconds = config.countdownDuration.inSeconds;

    emit(
      FaceVerificationState.countdown(
        config: config,
        countdownSeconds: countdownSeconds,
      ),
    );

    // Start countdown timer
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      countdownSeconds--;

      if (countdownSeconds <= 0) {
        timer.cancel();
        add(const RecordingStarted());
      } else {
        emit(state.copyWith(countdownSeconds: countdownSeconds));
      }
    });
  }

  /// Handles recording started event
  Future<void> _onRecordingStarted(
    RecordingStarted event,
    Emitter<FaceVerificationState> emit,
  ) async {
    try {
      await _videoStorageRepository.startRecording();

      final config = state.config;
      final recordingDuration = config.recordingDuration;

      emit(
        FaceVerificationState.recording(
          config: config,
          recordingProgress: 0,
          currentCoveragePercentage: 0,
          faceDetected: false,
        ),
      );

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Recording started',
          'Duration: ${recordingDuration.inSeconds}s',
        ),
      );

      // Start recording progress timer
      final startTime = DateTime.now();
      _recordingTimer =
          Timer.periodic(const Duration(milliseconds: 100), (timer) {
        final elapsed = DateTime.now().difference(startTime);
        final progress =
            (elapsed.inMilliseconds / recordingDuration.inMilliseconds * 100)
                .clamp(0.0, 100.0);

        emit(state.copyWith(recordingProgress: progress));

        if (elapsed >= recordingDuration) {
          timer.cancel();
          _stopRecording();
        }
      });

      // Start frame processing timer if face detection is enabled
      if (config.enableFaceDetection) {
        _frameProcessingTimer = Timer.periodic(
          config.frameProcessingInterval,
          (_) => _processFrame(),
        );
      }
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.criticalError,
          'Failed to start recording: $error',
          'Event: RecordingStarted',
        ),
        error,
        stackTrace,
      );

      add(FaceVerificationError(error: error, stackTrace: stackTrace));
    }
  }

  /// Processes a frame for face detection (simulated for now)
  void _processFrame() {
    // TODO: Implement actual face detection with ML Kit
    // For now, simulate face detection results
    _currentFaceDetected = true;
    _currentCoverage = 75.0; // Simulated coverage

    add(
      FaceDetectionFrameProcessed(
        coveragePercentage: _currentCoverage,
        faceDetected: _currentFaceDetected,
        qualityScore: _currentCoverage,
      ),
    );
  }

  /// Handles face detection frame processed event
  Future<void> _onFaceDetectionFrameProcessed(
    FaceDetectionFrameProcessed event,
    Emitter<FaceVerificationState> emit,
  ) async {
    _coverageHistory.add(event.coveragePercentage);

    emit(
      state.copyWith(
        currentCoveragePercentage: event.coveragePercentage,
        faceDetected: event.faceDetected,
      ),
    );
  }

  /// Stops recording and processes the video
  Future<void> _stopRecording() async {
    try {
      final videoPath = await _videoStorageRepository.stopRecording();
      add(RecordingCompleted(videoPath: videoPath));
    } catch (error, stackTrace) {
      add(FaceVerificationError(error: error, stackTrace: stackTrace));
    }
  }

  /// Handles recording completed event
  Future<void> _onRecordingCompleted(
    RecordingCompleted event,
    Emitter<FaceVerificationState> emit,
  ) async {
    _frameProcessingTimer?.cancel();

    emit(
      FaceVerificationState.validating(
        config: state.config,
        videoPath: event.videoPath,
      ),
    );

    // Start video validation
    add(const VideoValidationStarted());
  }

  /// Handles video validation started event
  Future<void> _onVideoValidationStarted(
    VideoValidationStarted event,
    Emitter<FaceVerificationState> emit,
  ) async {
    try {
      // Calculate coverage stats from recorded data
      final stats = FaceCoverageStats.fromDetectionResults(_coverageHistory);
      final qualityScore = stats.qualityScore;
      final meetsThreshold = qualityScore >= state.config.qualityThreshold;

      add(
        VideoValidationCompleted(
          qualityScore: qualityScore,
          meetsThreshold: meetsThreshold,
        ),
      );
    } catch (error, stackTrace) {
      add(FaceVerificationError(error: error, stackTrace: stackTrace));
    }
  }

  /// Handles video validation completed event
  Future<void> _onVideoValidationCompleted(
    VideoValidationCompleted event,
    Emitter<FaceVerificationState> emit,
  ) async {
    final stats = FaceCoverageStats.fromDetectionResults(_coverageHistory);

    emit(
      FaceVerificationState.validated(
        config: state.config,
        videoPath: state.videoPath!,
        coverageStats: stats,
        qualityScore: event.qualityScore,
        meetsQualityThreshold: event.meetsThreshold,
      ),
    );

    // Auto-upload if enabled and quality meets threshold
    if (state.config.autoUploadToFirebase && event.meetsThreshold) {
      add(const VideoUploadStarted());
    }
  }

  /// Handles video upload started event
  Future<void> _onVideoUploadStarted(
    VideoUploadStarted event,
    Emitter<FaceVerificationState> emit,
  ) async {
    emit(
      FaceVerificationState.uploading(
        config: state.config,
        videoPath: state.videoPath!,
        qualityScore: state.qualityScore,
        uploadProgress: 0,
      ),
    );

    try {
      await _videoStorageRepository.uploadVideo(
        localFilePath: state.videoPath!,
        qualityScore: state.qualityScore,
        deviceInfo: Platform.operatingSystem,
      );
    } catch (error, stackTrace) {
      add(FaceVerificationError(error: error, stackTrace: stackTrace));
    }
  }

  /// Handles video upload progress updated event
  Future<void> _onVideoUploadProgressUpdated(
    VideoUploadProgressUpdated event,
    Emitter<FaceVerificationState> emit,
  ) async {
    emit(state.copyWith(uploadProgress: event.progressPercentage));
  }

  /// Handles video upload completed event
  Future<void> _onVideoUploadCompleted(
    VideoUploadCompleted event,
    Emitter<FaceVerificationState> emit,
  ) async {
    try {
      // Get the uploaded video metadata from the repository
      final videos = await _videoStorageRepository.getUserVideos();
      final uploadedVideo = videos.firstWhere(
        (video) => video.id == event.videoId,
        orElse: () => throw Exception('Uploaded video not found'),
      );

      emit(
        FaceVerificationState.completed(
          config: state.config,
          uploadedVideo: uploadedVideo,
          qualityScore: state.qualityScore,
        ),
      );

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Verification completed',
          'VideoId: ${event.videoId}, Quality: ${state.qualityScore.toStringAsFixed(1)}%',
        ),
      );
    } catch (error, stackTrace) {
      add(FaceVerificationError(error: error, stackTrace: stackTrace));
    }
  }

  /// Handles face verification error event
  Future<void> _onFaceVerificationError(
    FaceVerificationError event,
    Emitter<FaceVerificationState> emit,
  ) async {
    _cleanupTimers();

    _logger.error(
      LoggingConstants.formatError(
        LoggingConstants.faceVerificationModule,
        LoggingConstants.criticalError,
        'Verification error: ${event.error}',
        'State: ${state.status}',
      ),
      event.error,
      event.stackTrace,
    );

    emit(
      FaceVerificationState.error(
        error: event.error,
        stackTrace: event.stackTrace,
        config: state.config,
      ),
    );
  }

  /// Handles face verification reset event
  Future<void> _onFaceVerificationReset(
    FaceVerificationReset event,
    Emitter<FaceVerificationState> emit,
  ) async {
    _cleanupTimers();
    _coverageHistory.clear();

    emit(FaceVerificationState.initial());
  }

  /// Handles retry face verification event
  Future<void> _onRetryFaceVerification(
    RetryFaceVerification event,
    Emitter<FaceVerificationState> emit,
  ) async {
    _cleanupTimers();
    _coverageHistory.clear();

    add(FaceVerificationStarted(config: state.config));
  }

  /// Cleans up all active timers
  void _cleanupTimers() {
    _countdownTimer?.cancel();
    _recordingTimer?.cancel();
    _frameProcessingTimer?.cancel();
  }

  @override
  Future<void> close() {
    _cleanupTimers();
    _uploadProgressSubscription?.cancel();
    _videoStorageRepository.dispose();
    return super.close();
  }
}
