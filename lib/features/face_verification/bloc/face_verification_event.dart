import 'package:equatable/equatable.dart';
import 'package:bloomg_flutter/features/face_verification/models/video_capture_config.dart';

/// {@template face_verification_event}
/// Base class for all face verification events.
/// {@endtemplate}
abstract class FaceVerificationEvent extends Equatable {
  /// {@macro face_verification_event}
  const FaceVerificationEvent();

  @override
  List<Object?> get props => [];
}

/// {@template face_verification_started}
/// Event triggered when face verification process is initiated.
/// {@endtemplate}
class FaceVerificationStarted extends FaceVerificationEvent {
  /// {@macro face_verification_started}
  const FaceVerificationStarted({
    this.config,
  });

  /// Configuration for the video capture process
  final VideoCaptureConfig? config;

  @override
  List<Object?> get props => [config];
}

/// {@template recording_countdown_started}
/// Event triggered when the countdown before recording begins.
/// {@endtemplate}
class RecordingCountdownStarted extends FaceVerificationEvent {
  /// {@macro recording_countdown_started}
  const RecordingCountdownStarted();
}

/// {@template recording_started}
/// Event triggered when video recording begins.
/// {@endtemplate}
class RecordingStarted extends FaceVerificationEvent {
  /// {@macro recording_started}
  const RecordingStarted();
}

/// {@template face_detection_frame_processed}
/// Event triggered when a frame is processed for face detection.
/// {@endtemplate}
class FaceDetectionFrameProcessed extends FaceVerificationEvent {
  /// {@macro face_detection_frame_processed}
  const FaceDetectionFrameProcessed({
    required this.coveragePercentage,
    required this.faceDetected,
    required this.qualityScore,
  });

  /// Face coverage percentage in the frame
  final double coveragePercentage;

  /// Whether a face was detected
  final bool faceDetected;

  /// Quality score of the detection
  final double qualityScore;

  @override
  List<Object?> get props => [coveragePercentage, faceDetected, qualityScore];
}

/// {@template recording_completed}
/// Event triggered when video recording is completed.
/// {@endtemplate}
class RecordingCompleted extends FaceVerificationEvent {
  /// {@macro recording_completed}
  const RecordingCompleted({
    required this.videoPath,
  });

  /// Path to the recorded video file
  final String videoPath;

  @override
  List<Object?> get props => [videoPath];
}

/// {@template video_validation_started}
/// Event triggered when video quality validation begins.
/// {@endtemplate}
class VideoValidationStarted extends FaceVerificationEvent {
  /// {@macro video_validation_started}
  const VideoValidationStarted();
}

/// {@template video_validation_completed}
/// Event triggered when video quality validation is completed.
/// {@endtemplate}
class VideoValidationCompleted extends FaceVerificationEvent {
  /// {@macro video_validation_completed}
  const VideoValidationCompleted({
    required this.qualityScore,
    required this.meetsThreshold,
  });

  /// Overall quality score of the video
  final double qualityScore;

  /// Whether the video meets the quality threshold
  final bool meetsThreshold;

  @override
  List<Object?> get props => [qualityScore, meetsThreshold];
}

/// {@template video_upload_started}
/// Event triggered when video upload to Firebase Storage begins.
/// {@endtemplate}
class VideoUploadStarted extends FaceVerificationEvent {
  /// {@macro video_upload_started}
  const VideoUploadStarted();
}

/// {@template video_upload_progress_updated}
/// Event triggered when video upload progress is updated.
/// {@endtemplate}
class VideoUploadProgressUpdated extends FaceVerificationEvent {
  /// {@macro video_upload_progress_updated}
  const VideoUploadProgressUpdated({
    required this.progressPercentage,
  });

  /// Upload progress as a percentage (0-100)
  final double progressPercentage;

  @override
  List<Object?> get props => [progressPercentage];
}

/// {@template video_upload_completed}
/// Event triggered when video upload is completed successfully.
/// {@endtemplate}
class VideoUploadCompleted extends FaceVerificationEvent {
  /// {@macro video_upload_completed}
  const VideoUploadCompleted({
    required this.videoId,
  });

  /// ID of the uploaded video
  final String videoId;

  @override
  List<Object?> get props => [videoId];
}

/// {@template face_verification_error}
/// Event triggered when an error occurs during face verification.
/// {@endtemplate}
class FaceVerificationError extends FaceVerificationEvent {
  /// {@macro face_verification_error}
  const FaceVerificationError({
    required this.error,
    this.stackTrace,
  });

  /// Error message or object
  final Object error;

  /// Optional stack trace
  final StackTrace? stackTrace;

  @override
  List<Object?> get props => [error, stackTrace];
}

/// {@template face_verification_reset}
/// Event triggered to reset the face verification state.
/// {@endtemplate}
class FaceVerificationReset extends FaceVerificationEvent {
  /// {@macro face_verification_reset}
  const FaceVerificationReset();
}

/// {@template retry_face_verification}
/// Event triggered to retry the face verification process.
/// {@endtemplate}
class RetryFaceVerification extends FaceVerificationEvent {
  /// {@macro retry_face_verification}
  const RetryFaceVerification();
}
