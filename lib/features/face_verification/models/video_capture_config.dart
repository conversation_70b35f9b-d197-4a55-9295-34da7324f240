import 'package:equatable/equatable.dart';

/// {@template video_capture_config}
/// Configuration model for face verification video capture.
/// 
/// Defines recording duration, quality thresholds, and other
/// parameters for the face verification process.
/// {@endtemplate}
class VideoCaptureConfig extends Equatable {
  /// {@macro video_capture_config}
  const VideoCaptureConfig({
    this.recordingDuration = const Duration(seconds: 9),
    this.countdownDuration = const Duration(seconds: 3),
    this.qualityThreshold = 70.0,
    this.minimumCoverageThreshold = 30.0,
    this.frameProcessingInterval = const Duration(milliseconds: 500),
    this.enableFaceDetection = true,
    this.enableQualityValidation = true,
    this.autoUploadToFirebase = true,
  });

  /// Duration of the video recording
  final Duration recordingDuration;

  /// Duration of the countdown before recording starts
  final Duration countdownDuration;

  /// Minimum quality score required for successful verification (0-100)
  final double qualityThreshold;

  /// Minimum face coverage percentage required for recording
  final double minimumCoverageThreshold;

  /// Interval between face detection frame processing
  final Duration frameProcessingInterval;

  /// Whether to enable real-time face detection during recording
  final bool enableFaceDetection;

  /// Whether to validate video quality after recording
  final bool enableQualityValidation;

  /// Whether to automatically upload videos to Firebase Storage
  final bool autoUploadToFirebase;

  /// Creates a copy of this config with updated fields
  VideoCaptureConfig copyWith({
    Duration? recordingDuration,
    Duration? countdownDuration,
    double? qualityThreshold,
    double? minimumCoverageThreshold,
    Duration? frameProcessingInterval,
    bool? enableFaceDetection,
    bool? enableQualityValidation,
    bool? autoUploadToFirebase,
  }) {
    return VideoCaptureConfig(
      recordingDuration: recordingDuration ?? this.recordingDuration,
      countdownDuration: countdownDuration ?? this.countdownDuration,
      qualityThreshold: qualityThreshold ?? this.qualityThreshold,
      minimumCoverageThreshold: minimumCoverageThreshold ?? this.minimumCoverageThreshold,
      frameProcessingInterval: frameProcessingInterval ?? this.frameProcessingInterval,
      enableFaceDetection: enableFaceDetection ?? this.enableFaceDetection,
      enableQualityValidation: enableQualityValidation ?? this.enableQualityValidation,
      autoUploadToFirebase: autoUploadToFirebase ?? this.autoUploadToFirebase,
    );
  }

  /// Default configuration for development/testing
  factory VideoCaptureConfig.development() {
    return const VideoCaptureConfig(
      recordingDuration: Duration(seconds: 5),
      countdownDuration: Duration(seconds: 2),
      qualityThreshold: 50.0,
      minimumCoverageThreshold: 20.0,
      autoUploadToFirebase: false,
    );
  }

  /// Production configuration with strict quality requirements
  factory VideoCaptureConfig.production() {
    return const VideoCaptureConfig(
      recordingDuration: Duration(seconds: 9),
      countdownDuration: Duration(seconds: 3),
      qualityThreshold: 80.0,
      minimumCoverageThreshold: 40.0,
      autoUploadToFirebase: true,
    );
  }

  /// Quick capture configuration for testing
  factory VideoCaptureConfig.quick() {
    return const VideoCaptureConfig(
      recordingDuration: Duration(seconds: 3),
      countdownDuration: Duration(seconds: 1),
      qualityThreshold: 30.0,
      minimumCoverageThreshold: 15.0,
      frameProcessingInterval: Duration(milliseconds: 1000),
      autoUploadToFirebase: false,
    );
  }

  @override
  List<Object?> get props => [
        recordingDuration,
        countdownDuration,
        qualityThreshold,
        minimumCoverageThreshold,
        frameProcessingInterval,
        enableFaceDetection,
        enableQualityValidation,
        autoUploadToFirebase,
      ];

  @override
  String toString() {
    return 'VideoCaptureConfig('
        'recordingDuration: ${recordingDuration.inSeconds}s, '
        'qualityThreshold: $qualityThreshold%, '
        'autoUpload: $autoUploadToFirebase)';
  }
}
