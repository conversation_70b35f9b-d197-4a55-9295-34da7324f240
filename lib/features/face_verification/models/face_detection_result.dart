import 'dart:ui';

import 'package:equatable/equatable.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';

/// {@template face_detection_result}
/// Model representing the result of face detection on a single frame.
/// 
/// Contains information about detected faces, coverage percentage,
/// and quality metrics for face verification.
/// {@endtemplate}
class FaceDetectionResult extends Equatable {
  /// {@macro face_detection_result}
  const FaceDetectionResult({
    required this.faceDetected,
    required this.faceCount,
    required this.coveragePercentage,
    required this.timestamp,
    this.boundingBox,
    this.confidence,
    this.headEulerAngleY,
    this.headEulerAngleZ,
    this.leftEyeOpenProbability,
    this.rightEyeOpenProbability,
    this.smilingProbability,
  });

  /// Whether at least one face was detected
  final bool faceDetected;

  /// Number of faces detected in the frame
  final int faceCount;

  /// Percentage of the frame covered by the primary face
  final double coveragePercentage;

  /// Timestamp when this detection was performed
  final DateTime timestamp;

  /// Bounding box of the primary detected face
  final Rect? boundingBox;

  /// Confidence score of the face detection (0.0 to 1.0)
  final double? confidence;

  /// Head rotation angle around Y-axis (yaw)
  final double? headEulerAngleY;

  /// Head rotation angle around Z-axis (roll)
  final double? headEulerAngleZ;

  /// Probability that the left eye is open (0.0 to 1.0)
  final double? leftEyeOpenProbability;

  /// Probability that the right eye is open (0.0 to 1.0)
  final double? rightEyeOpenProbability;

  /// Probability that the person is smiling (0.0 to 1.0)
  final double? smilingProbability;

  /// Creates a result with no face detected
  factory FaceDetectionResult.noFace() {
    return FaceDetectionResult(
      faceDetected: false,
      faceCount: 0,
      coveragePercentage: 0.0,
      timestamp: DateTime.now(),
    );
  }

  /// Creates a result from ML Kit Face detection
  factory FaceDetectionResult.fromMLKitFace(
    Face face,
    Size imageSize,
  ) {
    final boundingBox = face.boundingBox;
    final imageArea = imageSize.width * imageSize.height;
    final faceArea = boundingBox.width * boundingBox.height;
    final coveragePercentage = (faceArea / imageArea) * 100;

    return FaceDetectionResult(
      faceDetected: true,
      faceCount: 1,
      coveragePercentage: coveragePercentage.clamp(0.0, 100.0),
      timestamp: DateTime.now(),
      boundingBox: boundingBox,
      confidence: 1.0, // ML Kit doesn't provide confidence, assume high
      headEulerAngleY: face.headEulerAngleY,
      headEulerAngleZ: face.headEulerAngleZ,
      leftEyeOpenProbability: face.leftEyeOpenProbability,
      rightEyeOpenProbability: face.rightEyeOpenProbability,
      smilingProbability: face.smilingProbability,
    );
  }

  /// Creates a result from multiple ML Kit faces
  factory FaceDetectionResult.fromMLKitFaces(
    List<Face> faces,
    Size imageSize,
  ) {
    if (faces.isEmpty) {
      return FaceDetectionResult.noFace();
    }

    // Use the largest face as the primary face
    final primaryFace = faces.reduce((a, b) {
      final aArea = a.boundingBox.width * a.boundingBox.height;
      final bArea = b.boundingBox.width * b.boundingBox.height;
      return aArea > bArea ? a : b;
    });

    return FaceDetectionResult.fromMLKitFace(primaryFace, imageSize);
  }

  /// Creates a copy of this result with updated fields
  FaceDetectionResult copyWith({
    bool? faceDetected,
    int? faceCount,
    double? coveragePercentage,
    DateTime? timestamp,
    Rect? boundingBox,
    double? confidence,
    double? headEulerAngleY,
    double? headEulerAngleZ,
    double? leftEyeOpenProbability,
    double? rightEyeOpenProbability,
    double? smilingProbability,
  }) {
    return FaceDetectionResult(
      faceDetected: faceDetected ?? this.faceDetected,
      faceCount: faceCount ?? this.faceCount,
      coveragePercentage: coveragePercentage ?? this.coveragePercentage,
      timestamp: timestamp ?? this.timestamp,
      boundingBox: boundingBox ?? this.boundingBox,
      confidence: confidence ?? this.confidence,
      headEulerAngleY: headEulerAngleY ?? this.headEulerAngleY,
      headEulerAngleZ: headEulerAngleZ ?? this.headEulerAngleZ,
      leftEyeOpenProbability: leftEyeOpenProbability ?? this.leftEyeOpenProbability,
      rightEyeOpenProbability: rightEyeOpenProbability ?? this.rightEyeOpenProbability,
      smilingProbability: smilingProbability ?? this.smilingProbability,
    );
  }

  /// Whether this detection is suitable for recording
  bool get isSuitableForRecording {
    return faceDetected && 
           faceCount == 1 && 
           coveragePercentage >= 30.0;
  }

  /// Whether this detection meets high quality standards
  bool get isHighQuality {
    return faceDetected && 
           faceCount == 1 && 
           coveragePercentage >= 50.0 &&
           _hasGoodHeadPose &&
           _hasOpenEyes;
  }

  /// Whether the head pose is suitable (not too tilted)
  bool get _hasGoodHeadPose {
    if (headEulerAngleY == null || headEulerAngleZ == null) return true;
    
    return headEulerAngleY!.abs() <= 30.0 && headEulerAngleZ!.abs() <= 30.0;
  }

  /// Whether both eyes appear to be open
  bool get _hasOpenEyes {
    if (leftEyeOpenProbability == null || rightEyeOpenProbability == null) {
      return true;
    }
    
    return leftEyeOpenProbability! > 0.5 && rightEyeOpenProbability! > 0.5;
  }

  /// Quality score for this detection (0.0 to 100.0)
  double get qualityScore {
    if (!faceDetected) return 0.0;
    
    double score = coveragePercentage;
    
    // Bonus for single face
    if (faceCount == 1) score += 10.0;
    
    // Bonus for good head pose
    if (_hasGoodHeadPose) score += 10.0;
    
    // Bonus for open eyes
    if (_hasOpenEyes) score += 10.0;
    
    return score.clamp(0.0, 100.0);
  }

  @override
  List<Object?> get props => [
        faceDetected,
        faceCount,
        coveragePercentage,
        timestamp,
        boundingBox,
        confidence,
        headEulerAngleY,
        headEulerAngleZ,
        leftEyeOpenProbability,
        rightEyeOpenProbability,
        smilingProbability,
      ];
}
