import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:bloomg_flutter/features/face_verification/bloc/face_verification_bloc.dart';
import 'package:bloomg_flutter/features/face_verification/bloc/face_verification_event.dart';
import 'package:bloomg_flutter/features/face_verification/bloc/face_verification_state.dart';
import 'package:bloomg_flutter/features/face_verification/models/video_capture_config.dart';

/// {@template face_verification_screen}
/// Screen for face verification video recording and upload.
///
/// Provides UI for the complete face verification flow including countdown,
/// recording, validation, and upload progress.
/// {@endtemplate}
class FaceVerificationScreen extends StatelessWidget {
  /// {@macro face_verification_screen}
  const FaceVerificationScreen({super.key});

  /// Route name for navigation
  static const String routeName = '/face-verification';

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => FaceVerificationBloc(),
      child: const FaceVerificationView(),
    );
  }
}

/// {@template face_verification_view}
/// Main view widget for face verification screen.
/// {@endtemplate}
class FaceVerificationView extends StatelessWidget {
  /// {@macro face_verification_view}
  const FaceVerificationView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Face Verification'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: BlocConsumer<FaceVerificationBloc, FaceVerificationState>(
        listener: (context, state) {
          if (state.hasError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Error: ${state.error}'),
                backgroundColor: Colors.red,
              ),
            );
          } else if (state.isCompleted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Face verification completed successfully!'),
                backgroundColor: Colors.green,
                duration: Duration(seconds: 2),
              ),
            );

            // Navigate to video gallery after successful upload
            Future.delayed(const Duration(seconds: 2), () {
              if (context.mounted) {
                context.go('/video-gallery');
              }
            });
          }
        },
        builder: (context, state) {
          return Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildStatusCard(context, state),
                const SizedBox(height: 24),
                _buildProgressIndicators(context, state),
                const SizedBox(height: 24),
                _buildActionButtons(context, state),
              ],
            ),
          );
        },
      ),
    );
  }

  /// Builds the status card showing current verification state
  Widget _buildStatusCard(BuildContext context, FaceVerificationState state) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Status: ${_getStatusText(state.status)}',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            if (state.isCountdown)
              Text(
                'Starting in ${state.countdownSeconds} seconds...',
                style: Theme.of(context).textTheme.bodyLarge,
              ),
            if (state.isRecording) ...[
              Text(
                'Recording: ${state.recordingProgress.toStringAsFixed(1)}%',
                style: Theme.of(context).textTheme.bodyLarge,
              ),
              const SizedBox(height: 8),
              Text(
                'Face Coverage: ${state.currentCoveragePercentage.toStringAsFixed(1)}%',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              Text(
                'Face Detected: ${state.faceDetected ? "Yes" : "No"}',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
            if (state.status == FaceVerificationStatus.validated) ...[
              Text(
                'Quality Score: ${state.qualityScore.toStringAsFixed(1)}%',
                style: Theme.of(context).textTheme.bodyLarge,
              ),
              Text(
                'Quality Rating: ${state.qualityRating}',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              Text(
                'Meets Threshold: ${state.meetsQualityThreshold ? "Yes" : "No"}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: state.meetsQualityThreshold
                          ? Colors.green
                          : Colors.red,
                    ),
              ),
            ],
            if (state.isUploading)
              Text(
                'Upload Progress: ${state.uploadProgress.toStringAsFixed(1)}%',
                style: Theme.of(context).textTheme.bodyLarge,
              ),
            if (state.isCompleted && state.uploadedVideo != null)
              Text(
                'Video ID: ${state.uploadedVideo!.id}',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
          ],
        ),
      ),
    );
  }

  /// Builds progress indicators for recording and upload
  Widget _buildProgressIndicators(
    BuildContext context,
    FaceVerificationState state,
  ) {
    return Column(
      children: [
        if (state.isRecording) ...[
          Text(
            'Recording Progress',
            style: Theme.of(context).textTheme.titleSmall,
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: state.recordingProgress / 100,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).primaryColor,
            ),
          ),
          const SizedBox(height: 16),
        ],
        if (state.isUploading) ...[
          Text(
            'Upload Progress',
            style: Theme.of(context).textTheme.titleSmall,
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: state.uploadProgress / 100,
            backgroundColor: Colors.grey[300],
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
          ),
        ],
      ],
    );
  }

  /// Builds action buttons based on current state
  Widget _buildActionButtons(
    BuildContext context,
    FaceVerificationState state,
  ) {
    return Column(
      children: [
        if (state.status == FaceVerificationStatus.initial) ...[
          ElevatedButton(
            onPressed: () {
              context.read<FaceVerificationBloc>().add(
                    FaceVerificationStarted(
                      config: VideoCaptureConfig.development(),
                    ),
                  );
            },
            child: const Text('Start Face Verification'),
          ),
        ],
        if (state.status == FaceVerificationStatus.validated &&
            !state.config.autoUploadToFirebase) ...[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton(
                onPressed: state.meetsQualityThreshold
                    ? () {
                        context.read<FaceVerificationBloc>().add(
                              const VideoUploadStarted(),
                            );
                      }
                    : null,
                child: const Text('Upload Video'),
              ),
              OutlinedButton(
                onPressed: () {
                  context.read<FaceVerificationBloc>().add(
                        const RetryFaceVerification(),
                      );
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ],
        if (state.hasError || state.isCompleted) ...[
          const SizedBox(height: 16),
          OutlinedButton(
            onPressed: () {
              context.read<FaceVerificationBloc>().add(
                    const FaceVerificationReset(),
                  );
            },
            child: const Text('Reset'),
          ),
        ],
      ],
    );
  }

  /// Gets human-readable status text
  String _getStatusText(FaceVerificationStatus status) {
    switch (status) {
      case FaceVerificationStatus.initial:
        return 'Ready to start';
      case FaceVerificationStatus.countdown:
        return 'Preparing to record';
      case FaceVerificationStatus.recording:
        return 'Recording video';
      case FaceVerificationStatus.validating:
        return 'Validating video quality';
      case FaceVerificationStatus.validated:
        return 'Video validated';
      case FaceVerificationStatus.uploading:
        return 'Uploading to cloud';
      case FaceVerificationStatus.completed:
        return 'Completed successfully';
      case FaceVerificationStatus.error:
        return 'Error occurred';
    }
  }
}
