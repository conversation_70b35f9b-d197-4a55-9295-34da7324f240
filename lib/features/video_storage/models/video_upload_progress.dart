import 'package:equatable/equatable.dart';

/// {@template video_upload_progress}
/// Model representing the progress of a video upload to Firebase Storage.
/// 
/// Tracks upload status, progress percentage, and any errors that occur
/// during the upload process.
/// {@endtemplate}
class VideoUploadProgress extends Equatable {
  /// {@macro video_upload_progress}
  const VideoUploadProgress({
    required this.status,
    this.progressPercentage = 0.0,
    this.bytesTransferred = 0,
    this.totalBytes = 0,
    this.error,
    this.videoId,
  });

  /// Current status of the upload
  final VideoUploadStatus status;

  /// Upload progress as a percentage (0.0 to 100.0)
  final double progressPercentage;

  /// Number of bytes transferred so far
  final int bytesTransferred;

  /// Total number of bytes to transfer
  final int totalBytes;

  /// Error message if upload failed
  final String? error;

  /// ID of the video being uploaded
  final String? videoId;

  /// Creates an initial upload progress
  factory VideoUploadProgress.initial() {
    return const VideoUploadProgress(status: VideoUploadStatus.idle);
  }

  /// Creates an uploading progress state
  factory VideoUploadProgress.uploading({
    required double progressPercentage,
    required int bytesTransferred,
    required int totalBytes,
    String? videoId,
  }) {
    return VideoUploadProgress(
      status: VideoUploadStatus.uploading,
      progressPercentage: progressPercentage,
      bytesTransferred: bytesTransferred,
      totalBytes: totalBytes,
      videoId: videoId,
    );
  }

  /// Creates a completed upload state
  factory VideoUploadProgress.completed({required String videoId}) {
    return VideoUploadProgress(
      status: VideoUploadStatus.completed,
      progressPercentage: 100.0,
      videoId: videoId,
    );
  }

  /// Creates a failed upload state
  factory VideoUploadProgress.failed({
    required String error,
    String? videoId,
  }) {
    return VideoUploadProgress(
      status: VideoUploadStatus.failed,
      error: error,
      videoId: videoId,
    );
  }

  /// Creates a copy of this progress with updated fields
  VideoUploadProgress copyWith({
    VideoUploadStatus? status,
    double? progressPercentage,
    int? bytesTransferred,
    int? totalBytes,
    String? error,
    String? videoId,
  }) {
    return VideoUploadProgress(
      status: status ?? this.status,
      progressPercentage: progressPercentage ?? this.progressPercentage,
      bytesTransferred: bytesTransferred ?? this.bytesTransferred,
      totalBytes: totalBytes ?? this.totalBytes,
      error: error ?? this.error,
      videoId: videoId ?? this.videoId,
    );
  }

  /// Whether the upload is currently in progress
  bool get isUploading => status == VideoUploadStatus.uploading;

  /// Whether the upload completed successfully
  bool get isCompleted => status == VideoUploadStatus.completed;

  /// Whether the upload failed
  bool get isFailed => status == VideoUploadStatus.failed;

  /// Whether the upload is idle (not started)
  bool get isIdle => status == VideoUploadStatus.idle;

  /// Formatted progress string for display
  String get formattedProgress {
    if (totalBytes > 0) {
      final mbTransferred = bytesTransferred / (1024 * 1024);
      final mbTotal = totalBytes / (1024 * 1024);
      return '${mbTransferred.toStringAsFixed(1)} / ${mbTotal.toStringAsFixed(1)} MB';
    }
    return '${progressPercentage.toStringAsFixed(1)}%';
  }

  @override
  List<Object?> get props => [
        status,
        progressPercentage,
        bytesTransferred,
        totalBytes,
        error,
        videoId,
      ];
}

/// Enumeration of possible video upload statuses
enum VideoUploadStatus {
  /// Upload has not started
  idle,

  /// Upload is currently in progress
  uploading,

  /// Upload completed successfully
  completed,

  /// Upload failed with an error
  failed,
}
