import 'package:equatable/equatable.dart';

/// {@template video_download_state}
/// Model representing the state of a video download from Firebase Storage.
/// 
/// Tracks download status, progress, and local file path for video playback.
/// {@endtemplate}
class VideoDownloadState extends Equatable {
  /// {@macro video_download_state}
  const VideoDownloadState({
    required this.status,
    this.progressPercentage = 0.0,
    this.localFilePath,
    this.error,
    this.downloadUrl,
  });

  /// Current status of the download
  final VideoDownloadStatus status;

  /// Download progress as a percentage (0.0 to 100.0)
  final double progressPercentage;

  /// Local file path where video is cached
  final String? localFilePath;

  /// Error message if download failed
  final String? error;

  /// Firebase Storage download URL
  final String? downloadUrl;

  /// Creates an initial download state
  factory VideoDownloadState.initial() {
    return const VideoDownloadState(status: VideoDownloadStatus.notDownloaded);
  }

  /// Creates a downloading state
  factory VideoDownloadState.downloading({
    required double progressPercentage,
    String? downloadUrl,
  }) {
    return VideoDownloadState(
      status: VideoDownloadStatus.downloading,
      progressPercentage: progressPercentage,
      downloadUrl: downloadUrl,
    );
  }

  /// Creates a downloaded state
  factory VideoDownloadState.downloaded({
    required String localFilePath,
    String? downloadUrl,
  }) {
    return VideoDownloadState(
      status: VideoDownloadStatus.downloaded,
      progressPercentage: 100.0,
      localFilePath: localFilePath,
      downloadUrl: downloadUrl,
    );
  }

  /// Creates a failed download state
  factory VideoDownloadState.failed({
    required String error,
    String? downloadUrl,
  }) {
    return VideoDownloadState(
      status: VideoDownloadStatus.failed,
      error: error,
      downloadUrl: downloadUrl,
    );
  }

  /// Creates a streaming state (playing directly from URL)
  factory VideoDownloadState.streaming({required String downloadUrl}) {
    return VideoDownloadState(
      status: VideoDownloadStatus.streaming,
      downloadUrl: downloadUrl,
    );
  }

  /// Creates a copy of this state with updated fields
  VideoDownloadState copyWith({
    VideoDownloadStatus? status,
    double? progressPercentage,
    String? localFilePath,
    String? error,
    String? downloadUrl,
  }) {
    return VideoDownloadState(
      status: status ?? this.status,
      progressPercentage: progressPercentage ?? this.progressPercentage,
      localFilePath: localFilePath ?? this.localFilePath,
      error: error ?? this.error,
      downloadUrl: downloadUrl ?? this.downloadUrl,
    );
  }

  /// Whether the video is currently being downloaded
  bool get isDownloading => status == VideoDownloadStatus.downloading;

  /// Whether the video is downloaded and available locally
  bool get isDownloaded => status == VideoDownloadStatus.downloaded;

  /// Whether the download failed
  bool get isFailed => status == VideoDownloadStatus.failed;

  /// Whether the video is not downloaded
  bool get isNotDownloaded => status == VideoDownloadStatus.notDownloaded;

  /// Whether the video is being streamed
  bool get isStreaming => status == VideoDownloadStatus.streaming;

  /// Whether the video is ready for playback (downloaded or streaming)
  bool get isReadyForPlayback => isDownloaded || isStreaming;

  @override
  List<Object?> get props => [
        status,
        progressPercentage,
        localFilePath,
        error,
        downloadUrl,
      ];
}

/// Enumeration of possible video download statuses
enum VideoDownloadStatus {
  /// Video has not been downloaded
  notDownloaded,

  /// Video is currently being downloaded
  downloading,

  /// Video has been downloaded and is available locally
  downloaded,

  /// Video download failed
  failed,

  /// Video is being streamed directly from Firebase Storage
  streaming,
}
