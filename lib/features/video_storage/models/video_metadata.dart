import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';

/// {@template video_metadata}
/// Model representing video metadata stored in Firestore.
/// 
/// Contains information about uploaded face verification videos including
/// quality scores, timestamps, and Firebase Storage references.
/// {@endtemplate}
class VideoMetadata extends Equatable {
  /// {@macro video_metadata}
  const VideoMetadata({
    required this.id,
    required this.fileName,
    required this.uploadTimestamp,
    required this.qualityScore,
    required this.storageRef,
    this.thumbnailUrl,
    this.duration,
    this.fileSize,
    this.deviceInfo,
  });

  /// Unique identifier for the video
  final String id;

  /// Original file name of the video
  final String fileName;

  /// Timestamp when the video was uploaded to Firebase Storage
  final DateTime uploadTimestamp;

  /// Face verification quality score (0-100)
  final double qualityScore;

  /// Firebase Storage reference path
  final String storageRef;

  /// Optional thumbnail URL for the video
  final String? thumbnailUrl;

  /// Duration of the video in seconds
  final int? duration;

  /// File size in bytes
  final int? fileSize;

  /// Device information where video was recorded
  final String? deviceInfo;

  /// Creates a VideoMetadata from Firestore document data
  factory VideoMetadata.fromFirestore(
    DocumentSnapshot<Map<String, dynamic>> doc,
  ) {
    final data = doc.data()!;
    return VideoMetadata(
      id: doc.id,
      fileName: data['fileName'] as String,
      uploadTimestamp: (data['uploadTimestamp'] as Timestamp).toDate(),
      qualityScore: (data['qualityScore'] as num).toDouble(),
      storageRef: data['storageRef'] as String,
      thumbnailUrl: data['thumbnailUrl'] as String?,
      duration: data['duration'] as int?,
      fileSize: data['fileSize'] as int?,
      deviceInfo: data['deviceInfo'] as String?,
    );
  }

  /// Converts VideoMetadata to Firestore document data
  Map<String, dynamic> toFirestore() {
    return {
      'fileName': fileName,
      'uploadTimestamp': Timestamp.fromDate(uploadTimestamp),
      'qualityScore': qualityScore,
      'storageRef': storageRef,
      if (thumbnailUrl != null) 'thumbnailUrl': thumbnailUrl,
      if (duration != null) 'duration': duration,
      if (fileSize != null) 'fileSize': fileSize,
      if (deviceInfo != null) 'deviceInfo': deviceInfo,
    };
  }

  /// Creates a copy of this VideoMetadata with updated fields
  VideoMetadata copyWith({
    String? id,
    String? fileName,
    DateTime? uploadTimestamp,
    double? qualityScore,
    String? storageRef,
    String? thumbnailUrl,
    int? duration,
    int? fileSize,
    String? deviceInfo,
  }) {
    return VideoMetadata(
      id: id ?? this.id,
      fileName: fileName ?? this.fileName,
      uploadTimestamp: uploadTimestamp ?? this.uploadTimestamp,
      qualityScore: qualityScore ?? this.qualityScore,
      storageRef: storageRef ?? this.storageRef,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      duration: duration ?? this.duration,
      fileSize: fileSize ?? this.fileSize,
      deviceInfo: deviceInfo ?? this.deviceInfo,
    );
  }

  /// Whether this video meets the quality threshold for verification
  bool get meetsQualityThreshold => qualityScore >= 70.0;

  /// Quality rating based on score
  String get qualityRating {
    if (qualityScore >= 80) return 'Excellent';
    if (qualityScore >= 70) return 'Good';
    if (qualityScore >= 50) return 'Moderate';
    return 'Poor';
  }

  /// Formatted file size string
  String get formattedFileSize {
    if (fileSize == null) return 'Unknown';
    
    final sizeInMB = fileSize! / (1024 * 1024);
    if (sizeInMB >= 1) {
      return '${sizeInMB.toStringAsFixed(1)} MB';
    }
    
    final sizeInKB = fileSize! / 1024;
    return '${sizeInKB.toStringAsFixed(0)} KB';
  }

  /// Formatted duration string
  String get formattedDuration {
    if (duration == null) return 'Unknown';
    
    final minutes = duration! ~/ 60;
    final seconds = duration! % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  @override
  List<Object?> get props => [
        id,
        fileName,
        uploadTimestamp,
        qualityScore,
        storageRef,
        thumbnailUrl,
        duration,
        fileSize,
        deviceInfo,
      ];
}
