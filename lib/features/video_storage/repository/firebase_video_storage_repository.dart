import 'dart:async';
import 'dart:io';

import 'package:bloomg_flutter/features/video_storage/models/video_metadata.dart';
import 'package:bloomg_flutter/features/video_storage/models/video_upload_progress.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';

import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';

/// {@template firebase_video_storage_repository}
/// Repository for managing video storage operations with Firebase Storage and Firestore.
///
/// Handles video uploads, downloads, metadata management, and progress tracking
/// for face verification videos.
/// {@endtemplate}
class FirebaseVideoStorageRepository {
  /// {@macro firebase_video_storage_repository}
  FirebaseVideoStorageRepository({
    FirebaseStorage? firebaseStorage,
    FirebaseFirestore? firebaseFirestore,
    FirebaseAuth? firebaseAuth,
  })  : _storage = firebaseStorage ?? FirebaseStorage.instance,
        _firestore = firebaseFirestore ?? FirebaseFirestore.instance,
        _auth = firebaseAuth ?? FirebaseAuth.instance;

  final FirebaseStorage _storage;
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;
  final LoggerService _logger = LoggerService();
  final Uuid _uuid = const Uuid();

  /// Stream controller for upload progress
  final StreamController<VideoUploadProgress> _uploadProgressController =
      StreamController<VideoUploadProgress>.broadcast();

  /// Stream of upload progress updates
  Stream<VideoUploadProgress> get uploadProgress =>
      _uploadProgressController.stream;

  /// Uploads a video file to Firebase Storage and saves metadata to Firestore
  Future<VideoMetadata> uploadVideo({
    required String localFilePath,
    required double qualityScore,
    String? deviceInfo,
  }) async {
    final user = _auth.currentUser;
    if (user == null) {
      throw Exception('User must be authenticated to upload videos');
    }

    final file = File(localFilePath);
    if (!file.existsSync()) {
      throw Exception('Video file does not exist: $localFilePath');
    }

    final videoId = _uuid.v4();
    final fileName =
        'face_verification_${DateTime.now().millisecondsSinceEpoch}.mp4';
    final storageRef = 'face_verification_videos/${user.uid}/$videoId.mp4';

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.videoStorageModule,
        'Upload started',
        'VideoId: $videoId, File: $fileName',
      ),
    );

    try {
      // Create Firebase Storage reference
      final ref = _storage.ref().child(storageRef);

      // Start upload with progress tracking
      final uploadTask = ref.putFile(file);

      // Listen to upload progress
      uploadTask.snapshotEvents.listen(
        (TaskSnapshot snapshot) {
          final progress =
              snapshot.bytesTransferred / snapshot.totalBytes * 100;
          _uploadProgressController.add(
            VideoUploadProgress.uploading(
              progressPercentage: progress,
              bytesTransferred: snapshot.bytesTransferred,
              totalBytes: snapshot.totalBytes,
              videoId: videoId,
            ),
          );
        },
        onError: (Object error) {
          _uploadProgressController.add(
            VideoUploadProgress.failed(
              error: error.toString(),
              videoId: videoId,
            ),
          );
        },
      );

      // Wait for upload completion
      final snapshot = await uploadTask;
      final fileSize = snapshot.totalBytes;

      // Create video metadata
      final metadata = VideoMetadata(
        id: videoId,
        fileName: fileName,
        uploadTimestamp: DateTime.now(),
        qualityScore: qualityScore,
        storageRef: storageRef,
        fileSize: fileSize,
        deviceInfo: deviceInfo,
      );

      // Save metadata to Firestore
      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('verificationVideos')
          .doc(videoId)
          .set(metadata.toFirestore());

      _uploadProgressController.add(
        VideoUploadProgress.completed(videoId: videoId),
      );

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.videoStorageModule,
          'Upload completed',
          'VideoId: $videoId, Size: ${metadata.formattedFileSize}',
        ),
      );

      return metadata;
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoStorageModule,
          LoggingConstants.criticalError,
          'Video upload failed: $error',
          'VideoId: $videoId',
        ),
        error,
        stackTrace,
      );

      _uploadProgressController.add(
        VideoUploadProgress.failed(
          error: error.toString(),
          videoId: videoId,
        ),
      );

      rethrow;
    }
  }

  /// Retrieves all video metadata for the current user
  Future<List<VideoMetadata>> getUserVideos() async {
    final user = _auth.currentUser;
    if (user == null) {
      throw Exception('User must be authenticated to retrieve videos');
    }

    try {
      final querySnapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('verificationVideos')
          .orderBy('uploadTimestamp', descending: true)
          .get();

      return querySnapshot.docs.map(VideoMetadata.fromFirestore).toList();
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoStorageModule,
          LoggingConstants.recoverableError,
          'Failed to retrieve user videos: $error',
          'UserId: ${user.uid}',
        ),
        error,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Gets a stream of video metadata updates for the current user
  Stream<List<VideoMetadata>> getUserVideosStream() {
    final user = _auth.currentUser;
    if (user == null) {
      throw Exception('User must be authenticated to stream videos');
    }

    return _firestore
        .collection('users')
        .doc(user.uid)
        .collection('verificationVideos')
        .orderBy('uploadTimestamp', descending: true)
        .snapshots()
        .map(
          (snapshot) => snapshot.docs.map(VideoMetadata.fromFirestore).toList(),
        );
  }

  /// Gets download URL for a video
  Future<String> getVideoDownloadUrl(String storageRef) async {
    try {
      final ref = _storage.ref().child(storageRef);
      return await ref.getDownloadURL();
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoStorageModule,
          LoggingConstants.recoverableError,
          'Failed to get download URL: $error',
          'StorageRef: $storageRef',
        ),
        error,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Downloads a video to local storage for offline playback
  Future<String> downloadVideoToLocal(VideoMetadata metadata) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final localPath = '${appDir.path}/videos/${metadata.id}.mp4';

      // Create videos directory if it doesn't exist
      final videosDir = Directory('${appDir.path}/videos');
      if (!videosDir.existsSync()) {
        videosDir.createSync(recursive: true);
      }

      // Download file using Firebase Storage
      final ref = _storage.ref().child(metadata.storageRef);
      final file = File(localPath);
      await ref.writeToFile(file);

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.videoStorageModule,
          'Video downloaded',
          'VideoId: ${metadata.id}, LocalPath: $localPath',
        ),
      );

      return localPath;
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoStorageModule,
          LoggingConstants.recoverableError,
          'Failed to download video: $error',
          'VideoId: ${metadata.id}',
        ),
        error,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Deletes a video and its metadata
  Future<void> deleteVideo(VideoMetadata metadata) async {
    final user = _auth.currentUser;
    if (user == null) {
      throw Exception('User must be authenticated to delete videos');
    }

    try {
      // Delete from Firebase Storage
      final ref = _storage.ref().child(metadata.storageRef);
      await ref.delete();

      // Delete metadata from Firestore
      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('verificationVideos')
          .doc(metadata.id)
          .delete();

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.videoStorageModule,
          'Video deleted',
          'VideoId: ${metadata.id}',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoStorageModule,
          LoggingConstants.criticalError,
          'Failed to delete video: $error',
          'VideoId: ${metadata.id}',
        ),
        error,
        stackTrace,
      );
      rethrow;
    }
  }

  /// Disposes resources
  void dispose() {
    _uploadProgressController.close();
  }
}
