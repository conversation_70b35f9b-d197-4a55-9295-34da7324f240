import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:bloomg_flutter/features/face_verification/face_verification.dart';
import 'package:bloomg_flutter/features/video_gallery/video_gallery.dart';
import 'package:bloomg_flutter/counter/counter.dart';

/// {@template app_router}
/// Application router configuration using go_router.
/// 
/// Defines all routes and navigation logic for the app.
/// {@endtemplate}
class AppRouter {
  /// {@macro app_router}
  static GoRouter get router => _router;

  static final GoRouter _router = GoRouter(
    initialLocation: '/',
    debugLogDiagnostics: true,
    routes: [
      // Home route (Counter page for now)
      GoRoute(
        path: '/',
        name: 'home',
        builder: (context, state) => const CounterPage(),
      ),

      // Face verification route
      GoRoute(
        path: '/face-verification',
        name: 'face-verification',
        builder: (context, state) => const FaceVerificationScreen(),
      ),

      // Video gallery route
      GoRoute(
        path: '/video-gallery',
        name: 'video-gallery',
        builder: (context, state) => const VideoGalleryScreen(),
      ),

      // Video player route (for full-screen playback)
      GoRoute(
        path: '/video-player/:videoId',
        name: 'video-player',
        builder: (context, state) {
          final videoId = state.pathParameters['videoId']!;
          return VideoPlayerScreen(videoId: videoId);
        },
      ),
    ],
    
    // Error handling
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(
        title: const Text('Page Not Found'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Page not found',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'The page "${state.uri}" could not be found.',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go('/'),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    ),
  );
}

/// {@template video_player_screen}
/// Full-screen video player screen.
/// 
/// Displays a video in full-screen mode with playback controls.
/// {@endtemplate}
class VideoPlayerScreen extends StatelessWidget {
  /// {@macro video_player_screen}
  const VideoPlayerScreen({
    super.key,
    required this.videoId,
  });

  /// ID of the video to play
  final String videoId;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Video Player'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => context.pop(),
        ),
      ),
      backgroundColor: Colors.black,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.play_circle_outline,
              size: 64,
              color: Colors.white,
            ),
            const SizedBox(height: 16),
            Text(
              'Video Player',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Video ID: $videoId',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.white70,
              ),
            ),
            const SizedBox(height: 24),
            const Text(
              'Video player implementation coming soon',
              style: TextStyle(color: Colors.white70),
            ),
          ],
        ),
      ),
    );
  }
}
