/// {@template logging_constants}
/// Constants for structured logging throughout the application.
/// 
/// Provides consistent logging formats, module names, and error types
/// for better log analysis and debugging.
/// {@endtemplate}
class LoggingConstants {
  /// Private constructor to prevent instantiation
  const LoggingConstants._();

  // Module names for categorizing logs
  static const String authModule = 'AUTH';
  static const String faceVerificationModule = 'FACE_VERIFICATION';
  static const String videoStorageModule = 'VIDEO_STORAGE';
  static const String videoGalleryModule = 'VIDEO_GALLERY';
  static const String navigationModule = 'NAVIGATION';
  static const String cameraModule = 'CAMERA';

  // Error severity levels
  static const String criticalError = 'CRITICAL';
  static const String recoverableError = 'RECOVERABLE';
  static const String warning = 'WARNING';
  static const String info = 'INFO';

  // Auth-specific action types
  static const String loginAttempt = 'LOGIN_ATTEMPT';
  static const String loginSuccess = 'LOGIN_SUCCESS';
  static const String loginFailure = 'LOGIN_FAILURE';
  static const String signupAttempt = 'SIGNUP_ATTEMPT';
  static const String signupSuccess = 'SIGNUP_SUCCESS';
  static const String signupFailure = 'SIGNUP_FAILURE';
  static const String logoutAttempt = 'LOGOUT_ATTEMPT';
  static const String logoutSuccess = 'LOGOUT_SUCCESS';
  static const String logoutFailure = 'LOGOUT_FAILURE';
  static const String passwordResetAttempt = 'PASSWORD_RESET_ATTEMPT';
  static const String passwordResetSuccess = 'PASSWORD_RESET_SUCCESS';
  static const String passwordResetFailure = 'PASSWORD_RESET_FAILURE';

  // Video-specific action types
  static const String videoUploadStarted = 'VIDEO_UPLOAD_STARTED';
  static const String videoUploadProgress = 'VIDEO_UPLOAD_PROGRESS';
  static const String videoUploadCompleted = 'VIDEO_UPLOAD_COMPLETED';
  static const String videoUploadFailed = 'VIDEO_UPLOAD_FAILED';
  static const String videoDownloadStarted = 'VIDEO_DOWNLOAD_STARTED';
  static const String videoDownloadCompleted = 'VIDEO_DOWNLOAD_COMPLETED';
  static const String videoDownloadFailed = 'VIDEO_DOWNLOAD_FAILED';
  static const String videoDeleted = 'VIDEO_DELETED';

  // Face verification action types
  static const String faceDetectionStarted = 'FACE_DETECTION_STARTED';
  static const String faceDetectionCompleted = 'FACE_DETECTION_COMPLETED';
  static const String recordingStarted = 'RECORDING_STARTED';
  static const String recordingCompleted = 'RECORDING_COMPLETED';
  static const String recordingFailed = 'RECORDING_FAILED';

  /// Formats a standard log message
  static String formatMessage(
    String module,
    String action,
    String details,
  ) {
    return '[$module] $action: $details';
  }

  /// Formats an error log message
  static String formatError(
    String module,
    String severity,
    String error,
    String context,
  ) {
    return '[$module] $severity: $error | Context: $context';
  }

  /// Formats a performance log message
  static String formatPerformance(
    String operation,
    Duration duration,
    String context,
  ) {
    return '[PERFORMANCE] $operation completed in ${duration.inMilliseconds}ms | $context';
  }

  /// Formats an auth-specific log message
  static String formatAuth(
    String action,
    String status,
    String userIdentifier,
    String details,
  ) {
    return '[$authModule] $action: $status | User: $userIdentifier | $details';
  }

  /// Formats a video operation log message
  static String formatVideo(
    String action,
    String status,
    String videoId,
    String details,
  ) {
    return '[$videoStorageModule] $action: $status | VideoId: $videoId | $details';
  }

  /// Formats a face verification log message
  static String formatFaceVerification(
    String action,
    String status,
    String details,
  ) {
    return '[$faceVerificationModule] $action: $status | $details';
  }
}
