import 'package:logger/logger.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';

/// {@template logger_service}
/// Centralized logging service for the application.
/// 
/// Provides structured logging with different levels and formats
/// for development, staging, and production environments.
/// {@endtemplate}
class LoggerService {
  /// {@macro logger_service}
  LoggerService() : _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 2,
      errorMethodCount: 8,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      printTime: true,
    ),
  );

  final Logger _logger;

  /// Logs an info message
  void info(String message) {
    _logger.i(message);
  }

  /// Logs a debug message
  void debug(String message) {
    _logger.d(message);
  }

  /// Logs a warning message
  void warning(String message) {
    _logger.w(message);
  }

  /// Logs an error message with optional error object and stack trace
  void error(
    String message, [
    Object? error,
    StackTrace? stackTrace,
  ]) {
    _logger.e(message, error: error, stackTrace: stackTrace);
  }

  /// Logs a fatal error message
  void fatal(
    String message, [
    Object? error,
    StackTrace? stackTrace,
  ]) {
    _logger.f(message, error: error, stackTrace: stackTrace);
  }

  /// Logs an authentication-related message
  void logAuth(
    String action,
    String status,
    String userIdentifier,
    String details,
  ) {
    final message = LoggingConstants.formatAuth(
      action,
      status,
      userIdentifier,
      details,
    );
    
    if (status.toLowerCase().contains('fail') || 
        status.toLowerCase().contains('error')) {
      error(message);
    } else {
      info(message);
    }
  }

  /// Logs a performance measurement
  void logPerformance(
    String operation,
    Duration duration,
    String context,
  ) {
    final message = LoggingConstants.formatPerformance(
      operation,
      duration,
      context,
    );
    
    // Log as warning if operation took too long
    if (duration.inMilliseconds > 5000) {
      warning(message);
    } else {
      info(message);
    }
  }

  /// Logs a video operation
  void logVideo(
    String action,
    String status,
    String videoId,
    String details,
  ) {
    final message = LoggingConstants.formatVideo(
      action,
      status,
      videoId,
      details,
    );
    
    if (status.toLowerCase().contains('fail') || 
        status.toLowerCase().contains('error')) {
      error(message);
    } else {
      info(message);
    }
  }

  /// Logs a face verification operation
  void logFaceVerification(
    String action,
    String status,
    String details,
  ) {
    final message = LoggingConstants.formatFaceVerification(
      action,
      status,
      details,
    );
    
    if (status.toLowerCase().contains('fail') || 
        status.toLowerCase().contains('error')) {
      error(message);
    } else {
      info(message);
    }
  }
}
