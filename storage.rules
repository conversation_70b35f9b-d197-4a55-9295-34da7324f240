rules_version = '2';

// Firebase Storage Security Rules for Face Verification Videos
service firebase.storage {
  match /b/{bucket}/o {
    // Face verification videos - user can only access their own videos
    match /face_verification_videos/{userId}/{videoId} {
      // Allow read/write only if the user is authenticated and accessing their own videos
      allow read, write: if request.auth != null 
                        && request.auth.uid == userId
                        && isValidVideoFile();
      
      // Allow delete only for the video owner
      allow delete: if request.auth != null 
                   && request.auth.uid == userId;
    }
    
    // Video thumbnails - same access rules as videos
    match /video_thumbnails/{userId}/{thumbnailId} {
      allow read, write: if request.auth != null 
                        && request.auth.uid == userId
                        && isValidImageFile();
      
      allow delete: if request.auth != null 
                   && request.auth.uid == userId;
    }
    
    // Helper function to validate video file types and size
    function isValidVideoFile() {
      return request.resource.contentType.matches('video/.*') 
             && request.resource.size < 50 * 1024 * 1024; // 50MB limit
    }
    
    // Helper function to validate image file types for thumbnails
    function isValidImageFile() {
      return request.resource.contentType.matches('image/.*') 
             && request.resource.size < 5 * 1024 * 1024; // 5MB limit
    }
    
    // Deny all other access
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
